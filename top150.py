import asyncio
import json
import base64
import os
import sys
import tempfile
import pickle
from datetime import datetime, timedelta
from PyQt6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QLineEdit, QPushButton,
    QComboBox, QLabel, QMessageBox, QTextEdit, QHBoxLayout, QDialog,
    QFileDialog, QTabWidget, QScrollArea,
    QTableWidget, QTableWidgetItem, QHeaderView, QInputDialog
)
from PyQt6.QtCore import Qt, QTimer
from playwright.async_api import async_playwright
from googleapiclient.discovery import build
from google_auth_oauthlib.flow import InstalledAppFlow
import threading

# CONFIG
OAUTH2_BASE64 = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
SCOPES = ["https://www.googleapis.com/auth/spreadsheets"]

# Constants
FETCH_INTERVAL = 30 * 60  # 30 minutes in seconds
ITEMS_PER_PAGE = 10
MAX_RETRIES = 3
PAGE_LOAD_DELAY = 500  # milliseconds
STABLE_DELAY = 1000  # milliseconds for stability
CLUSTERS = ["ELHA", "HB", "FMCG", "MKB", "Lifestyle", "Fashion"]

HEADER = ["STT", "Shop ID", "Item ID", "Tên sản phẩm", "Link sản phẩm", "Đã bán", "Doanh thu", "Cluster"]
LOCAL_PATH = os.path.join(os.getenv("LOCALAPPDATA"), "Data All in One", "Dashboard")
ACCOUNT_FILE = os.path.join(LOCAL_PATH, "accounts.json")

# Ensure directory exists
os.makedirs(LOCAL_PATH, exist_ok=True)

# OAuth2 login from BASE64
def get_user_credentials_from_base64():
    creds = None
    token_file = os.path.join(LOCAL_PATH, "token.pickle")
    if os.path.exists(token_file):
        os.remove(token_file)
    data = base64.b64decode(OAUTH2_BASE64).decode("utf-8")
    with tempfile.NamedTemporaryFile(delete=False, suffix=".json", mode="w", encoding="utf-8") as tmp_json:
        tmp_json.write(data)
        tmp_json_path = tmp_json.name
    flow = InstalledAppFlow.from_client_secrets_file(tmp_json_path, SCOPES)
    creds = flow.run_local_server(port=0)
    with open(token_file, "wb") as token:
        pickle.dump(creds, token)
    return creds

class ShopeeScraperApp(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Shopee Top Products Scraper")
        self.resize(600, 700)

        self.creds = get_user_credentials_from_base64()
        self.service = build("sheets", "v4", credentials=self.creds)

        self.spreadsheet_id = None
        self.sheet_names = []
        self.is_running = False
        self.user_confirm_event = threading.Event()

        # Cache để tránh API calls không cần thiết
        self._sheet_metadata_cache = None
        self._cache_timestamp = 0
        self._cache_ttl = 300  # 5 phút cache

        # Timeout optimization flags - EXTREME MODE
        self._skip_formatting = True  # Skip formatting để tăng tốc
        self._skip_validation = True  # Skip validation để tăng tốc
        self._fast_mode = True  # Mode tối ưu tốc độ
        self._ultra_fast_mode = True  # ULTRA FAST: Chỉ làm essential operations
        self._skip_charts = False  # Có thể skip charts nếu cần
        self._minimal_api_calls = True  # Giảm API calls xuống tối thiểu

        # Circuit breaker pattern
        self._timeout_count = 0
        self._max_timeouts = 3  # Sau 3 timeout liên tiếp, chuyển sang emergency mode
        self._emergency_mode = False
        self.first_sheet_update = {}  # Dictionary để theo dõi lần cập nhật đầu tiên cho mỗi sheet
        self.last_row_count = {}  # Dictionary để lưu số lượng dòng của lần cập nhật trước đó cho mỗi sheet

        self.accounts = self.load_accounts()

        self.layout = QVBoxLayout(self)

        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("Google Sheets URL")

        self.live_url_input = QLineEdit()
        self.live_url_input.setPlaceholderText("Live dashboard URL")

        self.load_button = QPushButton("Load Sheets")
        self.sheet_selector = QComboBox()

        # Thêm top selector
        self.top_label = QLabel("Chọn số lượng sản phẩm:")
        self.top_selector = QComboBox()
        for top in [100, 200, 300, 400, 500]:
            self.top_selector.addItem(f"Top {top}")

        # Thêm tùy chọn chế độ sử dụng
        self.mode_label = QLabel("Chế độ sử dụng:")
        self.mode_selector = QComboBox()
        self.mode_selector.addItem("Sử dụng tài khoản")
        self.mode_selector.addItem("Sử dụng Session File trực tiếp")
        self.mode_selector.currentTextChanged.connect(self.on_mode_changed)

        self.account_selector = QComboBox()
        for acc in self.accounts:
            self.account_selector.addItem(acc["label"])
        self.add_account_button = QPushButton("Thêm")
        self.edit_account_button = QPushButton("Sửa")
        self.delete_account_button = QPushButton("Xóa")

        # Session file selector (ẩn ban đầu)
        self.session_label = QLabel("Chọn Session File:")
        self.session_selector = QComboBox()
        self.refresh_session_button = QPushButton("🔄")
        self.refresh_session_button.setMaximumWidth(40)
        self.refresh_session_button.clicked.connect(self.refresh_session_list)

        # Ẩn session selector ban đầu
        self.session_label.setVisible(False)
        self.session_selector.setVisible(False)
        self.refresh_session_button.setVisible(False)

        self.start_button = QPushButton("Thực hiện")
        self.stop_button = QPushButton("Stop")
        self.stop_button.setEnabled(False)

        # Vô hiệu hóa nút "Thực hiện" nếu không có tài khoản nào và đang ở chế độ tài khoản
        self.update_start_button_state()

        self.log_output = QTextEdit()
        self.log_output.setReadOnly(True)
        self.log_output.setStyleSheet(
            "background:#23272e; color:#e0e0e0; font-family:Consolas,monospace; font-size:13px;"
            "border:1px solid #444; border-radius:6px; padding:8px;"
        )

        self.layout.addWidget(QLabel("Google Sheet URL:"))
        self.layout.addWidget(self.url_input)
        self.layout.addWidget(self.load_button)
        self.layout.addWidget(QLabel("Chọn sheet:"))
        self.layout.addWidget(self.sheet_selector)

        # Thêm top selector vào layout
        self.layout.addWidget(self.top_label)
        self.layout.addWidget(self.top_selector)

        # Thêm mode selector
        self.layout.addWidget(self.mode_label)
        self.layout.addWidget(self.mode_selector)

        # Account section
        self.account_label = QLabel("Chọn tài khoản:")
        self.layout.addWidget(self.account_label)
        acc_row = QHBoxLayout()
        acc_row.addWidget(self.account_selector)

        # Tạo layout cho các nút quản lý tài khoản
        acc_buttons = QHBoxLayout()
        acc_buttons.addWidget(self.add_account_button)
        acc_buttons.addWidget(self.edit_account_button)
        acc_buttons.addWidget(self.delete_account_button)

        # Thêm layout các nút vào layout chính
        acc_row.addLayout(acc_buttons)
        self.layout.addLayout(acc_row)

        # Session file section
        self.layout.addWidget(self.session_label)
        session_row = QHBoxLayout()
        session_row.addWidget(self.session_selector)
        session_row.addWidget(self.refresh_session_button)
        self.layout.addLayout(session_row)

        # Thêm nút Import Cookie từ Browser
        import_layout = QHBoxLayout()
        self.import_cookie_button = QPushButton("Import Cookie từ Browser")
        self.import_cookie_button.clicked.connect(self.import_cookie_from_browser)
        import_layout.addWidget(self.import_cookie_button)

        self.manage_sessions_button = QPushButton("Quản lý Session Files")
        self.manage_sessions_button.clicked.connect(self.manage_session_files)
        import_layout.addWidget(self.manage_sessions_button)

        import_layout_widget = QWidget()
        import_layout_widget.setLayout(import_layout)
        self.layout.addWidget(import_layout_widget)

        self.layout.addWidget(QLabel("URL phiên live:"))
        self.layout.addWidget(self.live_url_input)

        btn_row = QHBoxLayout()
        btn_row.addWidget(self.start_button)
        btn_row.addWidget(self.stop_button)
        self.recalculate_button = QPushButton("Tính toán lại")
        btn_row.addWidget(self.recalculate_button)

        # Thêm nút "Xem Timeline Analysis"
        self.timeline_button = QPushButton("📊 Timeline Analysis")
        self.timeline_button.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7B1FA2;
            }
        """)
        self.timeline_button.clicked.connect(self.open_timeline_analysis)
        btn_row.addWidget(self.timeline_button)

        self.layout.addLayout(btn_row)

        self.layout.addWidget(QLabel("Log xử lý:"))
        self.layout.addWidget(self.log_output)

        self.load_button.clicked.connect(self.load_sheets)
        self.start_button.clicked.connect(self.start_scraper)
        self.stop_button.clicked.connect(self.stop_scraper)
        self.add_account_button.clicked.connect(self.show_add_account_dialog)
        self.edit_account_button.clicked.connect(self.show_edit_account_dialog)
        self.delete_account_button.clicked.connect(self.delete_account)
        self.recalculate_button.clicked.connect(self.show_recalculate_dialog)

        # Load session files ban đầu
        self.refresh_session_list()

    def open_timeline_analysis(self):
        """Mở Google Sheets để xem Timeline Analysis"""
        try:
            spreadsheet_id = self.parse_spreadsheet_id(self.url_input.text().strip())
            if not spreadsheet_id:
                QMessageBox.warning(self, "Lỗi", "Vui lòng nhập URL Google Sheets hợp lệ!")
                return

            # Tạo URL trực tiếp đến sheet Historical Data
            historical_data_url = f"https://docs.google.com/spreadsheets/d/{spreadsheet_id}/edit#gid=0"

            # Thử tìm sheet ID của Historical Data
            try:
                import webbrowser
                # Mở URL trong trình duyệt mặc định
                webbrowser.open(historical_data_url)
                self.log("📊 Đã mở Timeline Analysis trong trình duyệt")

                # Hiển thị thông báo hướng dẫn
                QMessageBox.information(
                    self,
                    "Timeline Analysis",
                    "Đã mở Google Sheets!\n\n"
                    "Tìm sheet 'Historical Data' để xem:\n"
                    "• Biểu đồ xu hướng doanh thu theo khung giờ\n"
                    "• Biểu đồ xu hướng % market share\n"
                    "• Dữ liệu timeline chi tiết\n\n"
                    "Các biểu đồ sẽ tự động cập nhật khi có dữ liệu mới!"
                )

            except Exception as e:
                self.log(f"❌ Lỗi khi mở trình duyệt: {e}")
                # Fallback: copy URL vào clipboard
                try:
                    import pyperclip
                    pyperclip.copy(historical_data_url)
                    QMessageBox.information(
                        self,
                        "Timeline Analysis",
                        f"Không thể mở trình duyệt tự động.\n\n"
                        f"URL đã được copy vào clipboard:\n{historical_data_url}\n\n"
                        f"Vui lòng dán vào trình duyệt để xem Timeline Analysis!"
                    )
                except:
                    QMessageBox.information(
                        self,
                        "Timeline Analysis",
                        f"URL Timeline Analysis:\n{historical_data_url}\n\n"
                        f"Vui lòng copy URL này vào trình duyệt để xem biểu đồ!"
                    )

        except Exception as e:
            self.log(f"❌ Lỗi khi mở Timeline Analysis: {e}")
            QMessageBox.critical(self, "Lỗi", f"Không thể mở Timeline Analysis: {e}")

    def on_mode_changed(self):
        """Xử lý khi thay đổi chế độ sử dụng"""
        mode = self.mode_selector.currentText()

        if mode == "Sử dụng tài khoản":
            # Hiển thị account controls
            self.account_label.setVisible(True)
            self.account_selector.setVisible(True)
            self.add_account_button.setVisible(True)
            self.edit_account_button.setVisible(True)
            self.delete_account_button.setVisible(True)

            # Ẩn session controls
            self.session_label.setVisible(False)
            self.session_selector.setVisible(False)
            self.refresh_session_button.setVisible(False)
        else:
            # Ẩn account controls
            self.account_label.setVisible(False)
            self.account_selector.setVisible(False)
            self.add_account_button.setVisible(False)
            self.edit_account_button.setVisible(False)
            self.delete_account_button.setVisible(False)

            # Hiển thị session controls
            self.session_label.setVisible(True)
            self.session_selector.setVisible(True)
            self.refresh_session_button.setVisible(True)

        # Cập nhật trạng thái nút "Thực hiện"
        self.update_start_button_state()

    def update_start_button_state(self):
        """Cập nhật trạng thái nút Thực hiện dựa trên chế độ hiện tại"""
        mode = self.mode_selector.currentText()

        if mode == "Sử dụng tài khoản":
            # Chế độ tài khoản: cần có ít nhất 1 tài khoản
            self.start_button.setEnabled(len(self.accounts) > 0)
        else:
            # Chế độ session file: cần có ít nhất 1 session file
            self.start_button.setEnabled(self.session_selector.count() > 0)

    def refresh_session_list(self):
        """Làm mới danh sách session files"""
        self.session_selector.clear()

        if not os.path.exists(LOCAL_PATH):
            self.update_start_button_state()
            return

        session_files = []
        for file in os.listdir(LOCAL_PATH):
            if file.endswith('.json') and ('session' in file.lower() or 'auth_state' in file.lower()):
                file_path = os.path.join(LOCAL_PATH, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    if 'cookies' in data:
                        # Kiểm tra số lượng cookies và cookies quan trọng
                        cookies = data.get('cookies', [])
                        important_cookies = ['SPC_F', 'SPC_T_ID', 'shopee_token']
                        cookie_names = [c.get('name', '') for c in cookies]
                        has_important = any(name in cookie_names for name in important_cookies)

                        status = "✅" if has_important else "⚠️"
                        display_name = f"{status} {file} ({len(cookies)} cookies)"

                        self.session_selector.addItem(display_name, file_path)
                        session_files.append(file)
                except:
                    continue

        if session_files:
            self.log(f"Đã tìm thấy {len(session_files)} session file(s)")
        else:
            self.log("Không tìm thấy session file nào")

        # Cập nhật trạng thái nút "Thực hiện"
        self.update_start_button_state()

    async def execute_with_retry(self, request, error_message, max_attempts=5, initial_delay=3):
        """
        Thực hiện API request với cơ chế retry

        Args:
            request: Request object từ Google API
            error_message: Thông báo lỗi để hiển thị
            max_attempts: Số lần thử tối đa
            initial_delay: Thời gian delay ban đầu giữa các lần thử

        Returns:
            Kết quả của request hoặc None nếu hết số lần thử
        """
        for attempt in range(max_attempts):
            try:
                # Tăng timeout cho request nếu có thể
                if hasattr(request, '_http') and hasattr(request._http, 'timeout'):
                    request._http.timeout = 180  # 3 phút timeout

                return request.execute()
            except (TimeoutError, Exception) as e:
                error_str = str(e).lower()
                is_retryable = any(keyword in error_str for keyword in [
                    "timeout", "timed out", "deadline", "503", "502", "429",
                    "quota", "rate limit", "internal error", "backend error"
                ])

                if is_retryable:
                    # Circuit breaker: Track timeout count
                    self._timeout_count += 1
                    if self._timeout_count >= self._max_timeouts:
                        self._emergency_mode = True
                        self.log(f"🚨 EMERGENCY MODE: Quá nhiều timeout ({self._timeout_count}), chuyển sang mode tối thiểu")

                    if attempt < max_attempts - 1:
                        # Exponential backoff: 3s, 6s, 12s, 24s
                        delay = initial_delay * (2 ** attempt)
                        self.log(f"⏰ {error_message} - Timeout/Error, thử lại {attempt + 1}/{max_attempts} sau {delay}s...")
                        await asyncio.sleep(delay)
                    else:
                        self.log(f"❌ {error_message} - Hết số lần thử sau {max_attempts} attempts")
                        return None
                else:
                    self.log(f"❌ {error_message} - Lỗi không thể retry: {e}")
                    return None

    def load_accounts(self):
        if not os.path.exists(ACCOUNT_FILE): return []
        with open(ACCOUNT_FILE, "r", encoding="utf-8") as f:
            return json.load(f)

    def log(self, msg):
        time = datetime.now().strftime("[%H:%M:%S]")
        self.log_output.append(f"{time} {msg}")
        self.log_output.verticalScrollBar().setValue(self.log_output.verticalScrollBar().maximum())

    def parse_spreadsheet_id(self, url):
        try:
            return url.split("/d/")[1].split("/")[0]
        except:
            return None

    def load_sheets(self):
        self.spreadsheet_id = self.parse_spreadsheet_id(self.url_input.text().strip())
        if not self.spreadsheet_id:
            QMessageBox.critical(self, "Lỗi", "Không thể lấy spreadsheet ID")
            return
        try:
            meta = self.service.spreadsheets().get(spreadsheetId=self.spreadsheet_id).execute()
            self.sheet_names = [s["properties"]["title"] for s in meta["sheets"]]
            self.sheet_selector.clear()
            self.sheet_selector.addItems(self.sheet_names)

            # Tự động tìm và chọn sheet có tên TOP GMV hoặc tương đồng
            top_gmv_index = -1
            for i, sheet_name in enumerate(self.sheet_names):
                # Chuyển về chữ thường để dễ so sánh
                sheet_name_lower = sheet_name.lower()
                if "top" in sheet_name_lower and "gmv" in sheet_name_lower:
                    top_gmv_index = i
                    break

            # Nếu tìm thấy sheet phù hợp, tự động chọn
            if top_gmv_index >= 0:
                self.sheet_selector.setCurrentIndex(top_gmv_index)
                self.log(f"Đã tự động chọn sheet: {self.sheet_names[top_gmv_index]}")
                QMessageBox.information(self, "OK", f"Đã load danh sách sheet và tự động chọn sheet {self.sheet_names[top_gmv_index]}")
            else:
                # Nếu không tìm thấy sheet TOP GMV, tự động tạo sheet mới
                self.log("Không tìm thấy sheet TOP GMV, đang tạo sheet mới...")

                # Tạo sheet mới với tên "TOP GMV"
                sheet_properties = {
                    'requests': [{
                        'addSheet': {
                            'properties': {
                                'title': 'TOP GMV',
                                'gridProperties': {
                                    'rowCount': 1000,
                                    'columnCount': 20
                                }
                            }
                        }
                    }]
                }

                # Gửi request để tạo sheet mới
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body=sheet_properties
                ).execute()

                # Cập nhật lại danh sách sheet
                meta = self.service.spreadsheets().get(spreadsheetId=self.spreadsheet_id).execute()
                self.sheet_names = [s["properties"]["title"] for s in meta["sheets"]]
                self.sheet_selector.clear()
                self.sheet_selector.addItems(self.sheet_names)

                # Tìm và chọn sheet TOP GMV vừa tạo
                for i, sheet_name in enumerate(self.sheet_names):
                    if sheet_name == "TOP GMV":
                        self.sheet_selector.setCurrentIndex(i)
                        break

                self.log("Đã tạo và chọn sheet TOP GMV")
                QMessageBox.information(self, "OK", "Đã load danh sách sheet và tạo sheet TOP GMV mới")

                # Đánh dấu sheet này chưa được cập nhật lần đầu
                self.first_sheet_update["TOP GMV"] = False
        except Exception as e:
            QMessageBox.critical(self, "Lỗi", str(e))

    def show_user_confirm_dialog(self):
        msg = QMessageBox(self)
        msg.setWindowTitle("Xác nhận thao tác")
        msg.setText("Vui lòng sort Doanh thu, minimize trình duyệt nếu muốn, rồi bấm OK để bắt đầu scrape!")
        msg.setIcon(QMessageBox.Icon.Information)
        msg.setStandardButtons(QMessageBox.StandardButton.Ok)
        # Đặt cờ để dialog luôn hiển thị trên cùng
        msg.setWindowFlags(msg.windowFlags() | Qt.WindowType.WindowStaysOnTopHint)
        msg.buttonClicked.connect(lambda _: self.user_confirm_event.set())
        # Focus vào dialog để người dùng chú ý
        msg.activateWindow()
        msg.raise_()
        msg.exec()



    async def find_dashboard_tab(self, context, target_url):
        """
        Tìm tab nào đang có dashboard mở

        Args:
            context: Browser context từ Playwright
            target_url: URL phiên live cần tìm

        Returns:
            Page object của tab có dashboard, hoặc None nếu không tìm thấy
        """
        # Lấy ID phiên live từ URL
        live_id = None
        try:
            # Trích xuất ID từ URL dạng https://creator.shopee.vn/dashboard/live/ID
            if "dashboard/live/" in target_url:
                live_id = target_url.split("dashboard/live/")[-1].split("/")[0]
        except Exception:
            pass

        # Lấy danh sách tất cả các tab đang mở
        pages = context.pages

        # Giảm verbosity trong logs bằng cách không in ra mỗi URL đang quét
        dashboard_found = False

        for page in pages:
            try:
                current_url = page.url

                # Kiểm tra xem URL có chứa dashboard/live không
                if "dashboard/live/" in current_url:
                    dashboard_found = True
                    # Nếu có live_id cụ thể cần tìm
                    if live_id:
                        if f"dashboard/live/{live_id}" in current_url:
                            self.log(f"✅ Đã tìm thấy dashboard của phiên live {live_id}")
                            return page
                    else:
                        # Nếu không có live_id cụ thể, lấy tab dashboard đầu tiên tìm thấy
                        current_live_id = current_url.split("dashboard/live/")[-1].split("/")[0]
                        self.log(f"✅ Đã tìm thấy dashboard của phiên live {current_live_id}")
                        return page
            except Exception:
                continue

        if not dashboard_found and not target_url:
            self.log("Đang quét các tab để tìm dashboard Shopee...")

        return None

    def start_scraper(self):
        # Kiểm tra URL trước khi bắt đầu
        url = self.live_url_input.text().strip()
        if not url:
            self.log("URL phiên live không được cung cấp. Chương trình sẽ đợi bạn mở dashboard hoặc nhập URL...")

        self.is_running = True
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        # Chạy trong thread riêng biệt để UI vẫn phản hồi
        threading.Thread(target=lambda: asyncio.run(self.run_loop()), daemon=True).start()

    def stop_scraper(self):
        self.is_running = False
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.log("Đã dừng theo dõi.")

    async def run_loop(self):
        url = self.live_url_input.text().strip()

        # Lấy số lượng sản phẩm cần scrape từ top_selector
        selected_top = self.top_selector.currentText()
        num_products = int(selected_top.replace("Top ", ""))

        # Xác định session file dựa trên chế độ
        mode = self.mode_selector.currentText()

        if mode == "Sử dụng tài khoản":
            # Chế độ tài khoản - sử dụng logic cũ
            if not self.accounts:
                self.log("❌ Không có tài khoản nào được cấu hình!")
                return

            account = self.accounts[self.account_selector.currentIndex()]
            username, password = account["username"], account["password"]
            session_file = os.path.join(LOCAL_PATH, f"auth_state_{username}.json")

            # Nếu chưa có session, mở headful cho người dùng đăng nhập
            if not os.path.exists(session_file):
                self.log("Chưa có session, mở trình duyệt để đăng nhập mới (headful)...")
                async with async_playwright() as p:
                    browser = await p.firefox.launch(headless=False)
                    context = await browser.new_context()
                    page = await context.new_page()
                    await page.goto("https://creator.shopee.vn")
                    self.log("Đã truy cập trang đăng nhập Shopee...")
                    login_page = await self.fill_login(page, username, password)
                    # Nếu fill_login trả về một tab dashboard/home khác, đóng tab login cũ
                    if login_page != page:
                        await page.close()
                    await context.storage_state(path=session_file)
                    await browser.close()
                self.log("Đã lưu session, sẽ chuyển sang chế độ non-headless để scrape dữ liệu.")
        else:
            # Chế độ session file trực tiếp
            if self.session_selector.count() == 0:
                self.log("❌ Không có session file nào được tìm thấy!")
                return

            # Lấy đường dẫn session file từ combo box
            session_file = self.session_selector.currentData()
            if not session_file or not os.path.exists(session_file):
                self.log("❌ Session file không tồn tại!")
                return

            self.log(f"✅ Sử dụng session file: {os.path.basename(session_file)}")

            # Kiểm tra session file có hợp lệ không
            try:
                with open(session_file, 'r', encoding='utf-8') as f:
                    session_data = json.load(f)
                if 'cookies' not in session_data:
                    self.log("❌ Session file không hợp lệ - thiếu cookies!")
                    return

                cookies = session_data.get('cookies', [])
                important_cookies = ['SPC_F', 'SPC_T_ID', 'shopee_token']
                cookie_names = [c.get('name', '') for c in cookies]
                has_important = any(name in cookie_names for name in important_cookies)

                if not has_important:
                    self.log("⚠️ Session file có thể không hợp lệ - thiếu cookies quan trọng!")
                    reply = QMessageBox.question(self, "Xác nhận",
                        "Session file có thể không hợp lệ (thiếu cookies quan trọng). Bạn có muốn tiếp tục không?",
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
                    if reply != QMessageBox.StandardButton.Yes:
                        return

                self.log(f"✅ Session file hợp lệ với {len(cookies)} cookies")

            except Exception as e:
                self.log(f"❌ Lỗi khi đọc session file: {e}")
                return

        # Khởi tạo browser và giữ nó mở cho tất cả các lần scrape
        async with async_playwright() as p:
            self.log("Khởi tạo trình duyệt cho phiên làm việc...")
            browser = await p.firefox.launch(headless=False)
            context = await browser.new_context(storage_state=session_file)

            # Mở trang chính để đảm bảo session được load
            page = await context.new_page()
            await page.goto("https://creator.shopee.vn")
            self.log("Đã truy cập trang chính Shopee...")

            # Kiểm tra session có hợp lệ không
            session_valid = await self.validate_session(page, session_file)
            if not session_valid:
                self.log("⚠️ Session không hợp lệ...")
                await browser.close()

                if mode == "Sử dụng tài khoản":
                    # Chế độ tài khoản: xóa session cũ và tạo mới
                    self.log("Đang làm mới session cho tài khoản...")
                    if os.path.exists(session_file):
                        os.remove(session_file)
                    # Gọi lại hàm để tạo session mới
                    await self.run_loop()
                    return
                else:
                    # Chế độ session file trực tiếp: thông báo lỗi và dừng
                    self.log("❌ Session file không hợp lệ hoặc đã hết hạn!")
                    self.log("Vui lòng import session/cookies mới hoặc chọn session file khác.")
                    self.show_info_on_main_thread("Session không hợp lệ",
                        "Session file không hợp lệ hoặc đã hết hạn!\n\n"
                        "Vui lòng:\n"
                        "1. Import cookies/session mới từ trình duyệt\n"
                        "2. Hoặc chọn session file khác\n"
                        "3. Hoặc chuyển sang chế độ 'Sử dụng tài khoản'")
                    return

            # Kiểm tra xem đã có dashboard mở chưa
            dashboard_page = await self.find_dashboard_tab(context, url)
            dashboard_url = ""

            if not dashboard_page:
                # Nếu không tìm thấy dashboard và URL không được cung cấp
                if not url:
                    self.log("URL phiên live không được cung cấp. Vui lòng mở tab dashboard hoặc nhập URL...")
                    self.show_info_on_main_thread("Cần dashboard", "URL phiên live không được cung cấp. Vui lòng mở tab dashboard hoặc nhập URL phiên live, sau đó bấm OK để tiếp tục.")

                    # Đợi người dùng mở dashboard trong một tab mới
                    max_wait_time = 300  # 5 phút
                    start_time = datetime.now()

                    while True:
                        # Kiểm tra lại các tab để tìm dashboard
                        dashboard_page = await self.find_dashboard_tab(context, "")
                        if dashboard_page:
                            self.log("Đã tìm thấy dashboard được mở bởi người dùng")
                            break

                        # Kiểm tra nếu người dùng đã nhập URL
                        new_url = self.live_url_input.text().strip()
                        if new_url and new_url != url:
                            url = new_url
                            self.log(f"Đã phát hiện URL mới: {url}")
                            try:
                                await page.goto(url)
                                dashboard_page = page
                                break
                            except Exception:
                                self.log("URL không hợp lệ, vui lòng thử lại")

                        # Kiểm tra timeout
                        elapsed = (datetime.now() - start_time).total_seconds()
                        if elapsed > max_wait_time:
                            self.log("Đã hết thời gian chờ. Vui lòng nhập URL hoặc mở dashboard và thử lại.")
                            return  # Thoát khỏi hàm run_loop

                        await asyncio.sleep(2)  # Đợi 2 giây trước khi kiểm tra lại
                else:
                    # Nếu có URL, thử truy cập
                    try:
                        self.log(f"Đang truy cập URL phiên live: {url}")
                        await page.goto(url)
                        dashboard_page = page
                    except Exception as e:
                        self.log(f"Lỗi khi truy cập URL: {e}")
                        self.log("Vui lòng kiểm tra lại URL hoặc mở dashboard thủ công...")

                        # Đợi người dùng mở dashboard trong một tab mới
                        max_wait_time = 60  # 1 phút
                        start_time = datetime.now()

                        while True:
                            # Kiểm tra lại các tab để tìm dashboard
                            dashboard_page = await self.find_dashboard_tab(context, url)
                            if dashboard_page:
                                self.log("Đã tìm thấy dashboard được mở bởi người dùng")
                                break

                            # Kiểm tra timeout
                            elapsed = (datetime.now() - start_time).total_seconds()
                            if elapsed > max_wait_time:
                                self.log("Đã hết thời gian chờ. Vui lòng thử lại.")
                                return  # Thoát khỏi hàm run_loop

                            await asyncio.sleep(2)  # Đợi 2 giây trước khi kiểm tra lại
            else:
                # Đóng tab ban đầu vì không cần thiết nữa
                await page.close()

            # Đảm bảo đã tìm thấy dashboard
            if dashboard_page:
                dashboard_url = dashboard_page.url
                self.log(f"Đã tìm thấy dashboard tại: {dashboard_url}")
            else:
                self.log("Không thể tìm thấy dashboard. Vui lòng thử lại.")
                return  # Thoát khỏi hàm run_loop

            # Vòng lặp scrape dữ liệu với browser đã mở
            first_run = True
            while self.is_running:
                try:
                    # Kiểm tra xem URL có đúng định dạng dashboard/live không
                    # Đã loại bỏ kiểm tra tự động bỏ qua dialog

                    # Luôn hiển thị dialog xác nhận để người dùng sort dữ liệu
                    self.user_confirm_event.clear()
                    self.log("Vui lòng sort lại dữ liệu theo Doanh thu...")
                    QTimer.singleShot(0, self.show_user_confirm_dialog)
                    # Chờ người dùng xác nhận từ thread scraping
                    while not self.user_confirm_event.is_set():
                        await asyncio.sleep(0.5)

                    # Đợi 1 giây để trang load hoàn toàn sau khi người dùng đã sort
                    await asyncio.sleep(1)

                    if not first_run:
                        self.log(f"Chờ {FETCH_INTERVAL // 60} phút trước lần scrape tiếp theo...")
                        await asyncio.sleep(FETCH_INTERVAL)

                    if not self.is_running:
                        break

                    self.log("Bắt đầu scrape dữ liệu...")
                    # Reload trang để đảm bảo dữ liệu mới nhất (chỉ reload từ lần thứ 2)
                    if not first_run:
                        # Kiểm tra lại các tab để tìm dashboard trong trường hợp người dùng đã đóng tab
                        refreshed_dashboard = await self.find_dashboard_tab(context, dashboard_url)
                        if refreshed_dashboard:
                            dashboard_page = refreshed_dashboard
                        else:
                            # Nếu không tìm thấy, mở lại URL
                            try:
                                await dashboard_page.goto(dashboard_url)
                                # Đợi 1 giây sau khi load trang để đảm bảo ổn định
                                await asyncio.sleep(1)
                            except Exception:
                                await asyncio.sleep(2)
                                await dashboard_page.goto(dashboard_url)
                                # Đợi 1 giây sau khi load trang để đảm bảo ổn định
                                await asyncio.sleep(1)

                        # Kiểm tra xem URL có đúng định dạng dashboard/live không
                        # Đã loại bỏ kiểm tra tự động bỏ qua dialog

                        # Luôn hiển thị dialog xác nhận để người dùng sort dữ liệu
                        self.user_confirm_event.clear()
                        self.log("Vui lòng sort lại dữ liệu theo Doanh thu...")
                        QTimer.singleShot(0, self.show_user_confirm_dialog)
                        # Chờ người dùng xác nhận từ thread scraping
                        while not self.user_confirm_event.is_set():
                            await asyncio.sleep(0.5)

                        # Đợi 1 giây để trang load hoàn toàn sau khi người dùng đã sort
                        await asyncio.sleep(1)

                    # Thực hiện scrape
                    data = await self.extract_data(dashboard_page, num_products)

                    # Sau khi scrape xong, quay lại trang 1 bằng JavaScript để tránh timeout
                    try:
                        # Sử dụng JavaScript để click vào nút trang 1 thay vì DOM click
                        await dashboard_page.evaluate('''
                            () => {
                                const firstPageBtn = document.querySelector('li.eds-react-pagination-pager__page:first-child');
                                if (firstPageBtn) {
                                    firstPageBtn.click();
                                    return true;
                                }
                                return false;
                            }
                        ''')
                        self.log("Đã cố gắng quay lại trang 1 bằng JavaScript")

                        # Đợi một chút để trang load
                        await asyncio.sleep(1)
                    except Exception as e:
                        self.log(f"Lỗi khi quay lại trang 1: {e}")
                        self.log("Bỏ qua việc quay lại trang 1, tiếp tục xử lý")

                    # Tính tổng GMV
                    try:
                        total = sum([float((row[6].replace("₫", "").replace(".", "").replace(",", ".")).strip()) for row in data])
                        # Format tổng GMV theo định dạng dashboard để dễ đối chiếu
                        total_formatted = f"₫ {total:,.2f}".replace(",", "X").replace(".", ",").replace("X", ".")
                        self.log(f"✅ Đã cập nhật {selected_top}, tổng GMV là {total_formatted}")
                    except Exception as e:
                        self.log(f"❌ Lỗi khi tính tổng GMV: {e}")

                    # Đẩy dữ liệu lên Google Sheets
                    await self.push_to_sheet(data)
                    first_run = False

                    # Hiển thị thông báo hoàn thành
                    minutes_to_wait = FETCH_INTERVAL // 60
                    self.show_info_on_main_thread("Hoàn thành",
                        f"Đã hoàn thành quét {num_products} sản phẩm và cập nhật lên Google Sheets!")
                    self.log("✅ Quá trình quét đã hoàn thành. Chương trình vẫn đang chạy và sẽ quét lại sau khoảng thời gian đã cài đặt.")
                    self.log(f"⏱️ Đang chờ {minutes_to_wait} phút trước lần quét tiếp theo...")

                except Exception as e:
                    self.log(f"❌ Lỗi trong quá trình scrape: {e}")
                    await asyncio.sleep(10)  # Chờ một chút trước khi thử lại nếu có lỗi

            # Khi thoát vòng lặp (nhấn Stop), đóng browser
            self.log("Đóng trình duyệt...")
            await browser.close()
            self.log("Đã dừng theo dõi.")

    async def process(self):
        # Phương thức này không còn sử dụng vì đã chuyển logic vào run_loop
        pass

    async def fill_login(self, page, username, password):
        await page.fill('xpath=//*[@id="main"]/div/div[2]/div/div/div/div/div/div[2]/div/div[2]/form/div[1]/div[1]/input', username)
        await page.fill('xpath=//*[@id="main"]/div/div[2]/div/div/div/div/div/div[2]/div/div[2]/form/div[2]/div[1]/input', password)
        self.show_info_on_main_thread("Login", "Vui lòng đăng nhập và vượt CAPTCHA nếu có")
        # Đợi người dùng đăng nhập thủ công xong, nhưng chỉ đợi ở lần đầu (khi chưa có session)
        # Nếu đã có session, không cần đợi nữa để không block các lần cập nhật tiếp theo
        # Sử dụng biến cờ để chỉ đợi ở lần đầu
        if not hasattr(self, '_has_logged_in') or not self._has_logged_in:
            login_timeout = 300  # 5 phút timeout cho đăng nhập
            start_time = datetime.now()

            while True:
                # Kiểm tra URL hiện tại
                current_url = page.url

                # Kiểm tra nếu đã vào dashboard hoặc home
                if "dashboard" in current_url or "home" in current_url:
                    self.log("Đã đăng nhập thành công, phát hiện dashboard hoặc home page")
                    return page  # Trả về page hiện tại nếu đã vào dashboard/home

                # Kiểm tra timeout
                elapsed = (datetime.now() - start_time).total_seconds()
                if elapsed > login_timeout:
                    self.log("Hết thời gian chờ đăng nhập, tiếp tục với giả định đã đăng nhập")
                    break

                # Kiểm tra các tab khác trong context
                try:
                    context = page.context
                    all_pages = context.pages

                    # Nếu có nhiều hơn 1 tab, kiểm tra các tab khác
                    if len(all_pages) > 1:
                        for other_page in all_pages:
                            if other_page != page:
                                other_url = other_page.url
                                if "dashboard" in other_url or "home" in other_url:
                                    self.log(f"Phát hiện dashboard/home trong tab khác: {other_url}")
                                    return other_page  # Trả về tab dashboard/home để sử dụng
                except Exception as e:
                    self.log(f"Lỗi khi kiểm tra các tab khác: {e}")

                await asyncio.sleep(1)

            self._has_logged_in = True
            self.log("Hoàn tất quá trình đăng nhập")
            return page  # Trả về page hiện tại nếu không tìm thấy tab khác

    def show_info_on_main_thread(self, title, message):
        # Helper method để hiển thị thông báo từ main thread
        def show_message():
            msg = QMessageBox(self)
            msg.setWindowTitle(title)
            msg.setText(message)
            msg.setIcon(QMessageBox.Icon.Information)
            msg.setStandardButtons(QMessageBox.StandardButton.Ok)
            # Đặt cờ để dialog luôn hiển thị trên cùng
            msg.setWindowFlags(msg.windowFlags() | Qt.WindowType.WindowStaysOnTopHint)
            # Focus vào dialog để người dùng chú ý
            msg.activateWindow()
            msg.raise_()
            msg.exec()

        QTimer.singleShot(0, show_message)

    def show_countdown_timer(self, minutes, callback):
        """
        Hiển thị dialog đồng hồ đếm ngược và gọi callback khi hết thời gian

        Args:
            minutes: Số phút cần đếm ngược
            callback: Hàm sẽ được gọi khi hết thời gian
        """
        try:
            # Tạo dialog đếm ngược
            countdown_dialog = QDialog(self)
            countdown_dialog.setWindowTitle("Đồng hồ đếm ngược")
            countdown_dialog.setFixedSize(350, 200)
            countdown_dialog.setWindowFlags(countdown_dialog.windowFlags() | Qt.WindowType.WindowStaysOnTopHint)

            # Layout cho dialog
            layout = QVBoxLayout(countdown_dialog)

            # Label hiển thị thông báo
            message_label = QLabel("Đang chờ trước lần quét tiếp theo...")
            message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            message_label.setStyleSheet("font-size: 14px;")
            layout.addWidget(message_label)

            # Label hiển thị thời gian còn lại
            time_label = QLabel()
            time_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            time_label.setStyleSheet("font-size: 36px; font-weight: bold; color: #0066cc;")
            layout.addWidget(time_label)

            # Label thông báo
            info_label = QLabel("Khi hết thời gian, chương trình sẽ tự động tiếp tục quét.")
            info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            info_label.setWordWrap(True)
            layout.addWidget(info_label)

            # Nút để đóng dialog và tiếp tục ngay
            continue_button = QPushButton("Tiếp tục ngay")
            continue_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")
            continue_button.clicked.connect(lambda: (countdown_dialog.close(), callback()))
            layout.addWidget(continue_button)

            # Tính thời gian kết thúc
            seconds_total = minutes * 60
            end_time = datetime.now() + timedelta(seconds=seconds_total)

            # Hàm cập nhật thời gian
            def update_time():
                try:
                    remaining = (end_time - datetime.now()).total_seconds()
                    if remaining <= 0:
                        # Hết thời gian
                        self.log("⏱️ Đồng hồ đếm ngược đã kết thúc, tiếp tục quét...")
                        countdown_dialog.close()
                        callback()
                        return

                    # Tính phút và giây còn lại
                    mins = int(remaining // 60)
                    secs = int(remaining % 60)
                    time_label.setText(f"{mins:02d}:{secs:02d}")

                    # Cập nhật log mỗi phút
                    if secs == 0:
                        self.log(f"⏱️ Còn {mins} phút trước lần quét tiếp theo...")

                    # Tiếp tục cập nhật
                    QTimer.singleShot(1000, update_time)
                except Exception as e:
                    self.log(f"Lỗi khi cập nhật thời gian: {e}")

            # Bắt đầu cập nhật thời gian
            update_time()

            # Hiển thị dialog
            self.log(f"⏱️ Đang chờ {minutes} phút trước lần quét tiếp theo...")

            # Khi dialog đóng, cập nhật biến countdown_dialog_shown
            def on_dialog_closed():
                self.countdown_dialog_shown = False

            # Kết nối sự kiện finished với hàm on_dialog_closed
            countdown_dialog.finished.connect(on_dialog_closed)

            # Hiển thị dialog
            countdown_dialog.show()  # Sử dụng show() thay vì exec() để không chặn UI thread

        except Exception as e:
            self.log(f"❌ Lỗi khi hiển thị dialog đồng hồ đếm ngược: {e}")
            # Đảm bảo callback vẫn được gọi nếu có lỗi
            self.countdown_dialog_shown = False
            callback()

    async def extract_data(self, page, num_products):
        results = []
        # Tính số trang cần quét dựa trên số lượng sản phẩm
        num_pages = (num_products + ITEMS_PER_PAGE - 1) // ITEMS_PER_PAGE

        self.log(f"Sẽ quét {num_pages} trang để lấy {num_products} sản phẩm")

        for page_num in range(1, num_pages + 1):
            try:
                # Thêm cơ chế retry cho pagination
                for retry in range(MAX_RETRIES):
                    try:
                        # Giảm thời gian đợi để tăng tốc độ
                        await page.wait_for_timeout(PAGE_LOAD_DELAY)

                        # Tìm nút pagination
                        page_btns = await page.query_selector_all('li.eds-react-pagination-pager__page')
                        if not page_btns:
                            self.log(f"Không tìm thấy nút phân trang, đang thử lại ({retry+1}/{MAX_RETRIES})...")
                            if retry == MAX_RETRIES - 1:
                                self.log(f"Bỏ qua trang {page_num} do không tìm thấy nút phân trang")
                                break
                            await page.wait_for_timeout(PAGE_LOAD_DELAY)
                            continue

                        # Tìm nút trang hiện tại
                        clicked = False
                        for btn in page_btns:
                            text = await btn.inner_text()
                            if text.strip() == str(page_num):
                                # Sử dụng JavaScript click thay vì DOM click để tránh timeout và tăng tốc
                                await page.evaluate('(el) => el.click()', btn)
                                self.log(f"Đã click vào trang {page_num}")
                                clicked = True
                                break

                        if not clicked:
                            self.log(f"Không tìm thấy nút trang {page_num}, đang thử lại ({retry+1}/{MAX_RETRIES})...")
                            if retry == MAX_RETRIES - 1:
                                self.log(f"Bỏ qua trang {page_num} do không tìm thấy nút trang")
                                break
                            continue

                        # Đợi sau khi click để trang load
                        await page.wait_for_timeout(PAGE_LOAD_DELAY)
                        break  # Thoát khỏi vòng lặp retry nếu thành công

                    except Exception as e:
                        self.log(f"Lỗi khi chuyển trang {page_num}, lần thử {retry+1}: {e}")
                        if retry == MAX_RETRIES - 1:
                            self.log(f"Bỏ qua trang {page_num} sau {MAX_RETRIES} lần thử không thành công")
                            break

                # Thu thập dữ liệu từ các dòng trong bảng
                await page.wait_for_timeout(STABLE_DELAY)
                # Đợi thêm để đảm bảo ổn định
                await asyncio.sleep(0.5)
                rows = await page.query_selector_all('//table/tbody/tr')
                for row in rows:
                    try:
                        item_id = await row.get_attribute("data-row-key")
                        name_el = await row.query_selector('.index-module__titleText--WvajD')
                        sold_el = await row.query_selector('td:nth-child(5)')
                        rev_el = await row.query_selector('td:nth-child(6)')
                        name = await name_el.inner_text()
                        sold = int((await sold_el.inner_text()).replace(",", "").replace(".", ""))
                        # Lấy doanh thu nguyên bản, giữ nguyên định dạng từ dashboard
                        rev_text = await rev_el.inner_text()

                        # Thêm hai cột Shop ID và Link sản phẩm vào kết quả (để trống, sẽ dùng công thức sau)
                        results.append([len(results)+1, "", item_id, name, "", sold, rev_text])
                        if len(results) >= num_products:
                            return results
                    except Exception as e:
                        self.log(f"Lỗi khi xử lý dòng: {e}")
                        continue
            except Exception as e:
                self.log(f"Lỗi khi xử lý trang {page_num}: {e}")
        return results

    async def push_to_sheet(self, values):
        sheet = self.sheet_selector.currentText()

        # Xác định số dòng thực tế có dữ liệu
        actual_rows = len(values)
        last_row = actual_rows + 2  # Dòng cuối cùng có dữ liệu (dòng 3 là dòng đầu tiên của dữ liệu)
        total_row = last_row + 1    # Dòng để hiển thị tổng cộng

        # Kiểm tra xem đây có phải là lần cập nhật đầu tiên không
        is_first_update = sheet not in self.first_sheet_update or not self.first_sheet_update[sheet]

        if is_first_update:
            # Trong lần cập nhật đầu tiên, xóa toàn bộ dữ liệu cũ
            self.log("Lần cập nhật đầu tiên: xóa toàn bộ dữ liệu cũ")
            await self.execute_with_retry(
                self.service.spreadsheets().values().clear(
                    spreadsheetId=self.spreadsheet_id,
                    range=f"{sheet}!B3:I1000"  # Phạm vi đủ lớn để xóa hết dữ liệu cũ, bao gồm cả cột Cluster (I)
                ),
                "Lỗi khi xóa dữ liệu cũ"
            )

            # Không xóa bảng tổng hợp Cluster cũ, sẽ được xử lý trong phương thức create_cluster_summary_table
        else:
            # Trong các lần cập nhật tiếp theo, chỉ xóa các cột dữ liệu thực tế
            self.log("Lần cập nhật tiếp theo: chỉ xóa các cột dữ liệu thực tế")

            # Xóa cột Item ID (D)
            await self.execute_with_retry(
                self.service.spreadsheets().values().clear(
                    spreadsheetId=self.spreadsheet_id,
                    range=f"{sheet}!D3:D1000"
                ),
                "Lỗi khi xóa cột Item ID"
            )

            # Xóa cột Tên sản phẩm (E)
            await self.execute_with_retry(
                self.service.spreadsheets().values().clear(
                    spreadsheetId=self.spreadsheet_id,
                    range=f"{sheet}!E3:E1000"
                ),
                "Lỗi khi xóa cột Tên sản phẩm"
            )

            # Xóa cột Đã bán (G)
            await self.execute_with_retry(
                self.service.spreadsheets().values().clear(
                    spreadsheetId=self.spreadsheet_id,
                    range=f"{sheet}!G3:G1000"
                ),
                "Lỗi khi xóa cột Đã bán"
            )

            # Xóa cột Doanh thu (H)
            await self.execute_with_retry(
                self.service.spreadsheets().values().clear(
                    spreadsheetId=self.spreadsheet_id,
                    range=f"{sheet}!H3:H1000"
                ),
                "Lỗi khi xóa cột Doanh thu"
            )

        # Lấy sheetId từ tên sheet
        try:
            spreadsheet = await self.execute_with_retry(
                self.service.spreadsheets().get(spreadsheetId=self.spreadsheet_id),
                "Lỗi khi lấy thông tin sheet"
            )
            if not spreadsheet:
                self.log("❌ Không thể lấy thông tin sheet, bỏ qua quá trình cập nhật")
                return
            sheet_id = next(s['properties']['sheetId'] for s in spreadsheet['sheets'] if s['properties']['title'] == sheet)
        except Exception as e:
            self.log(f"❌ Lỗi khi lấy thông tin sheet: {e}")
            return

        # Tạo các request cho định dạng
        format_requests = [
            {
                "repeatCell": {
                    "range": {
                        "sheetId": sheet_id,
                        "startRowIndex": 1,
                        "endRowIndex": 2,
                        "startColumnIndex": 1,  # Chuyển từ 2 (cột C) sang 1 (cột B)
                        "endColumnIndex": 9    # Mở rộng để bao gồm cột Cluster (I)
                    },
                    "cell": {
                        "userEnteredFormat": {
                            "backgroundColor": {"red": 1, "green": 0.6, "blue": 0.2},
                            "horizontalAlignment": "CENTER",
                            "textFormat": {"bold": True, "foregroundColor": {"red": 1, "green": 1, "blue": 1}}
                        }
                    },
                    "fields": "userEnteredFormat(backgroundColor,textFormat,horizontalAlignment)"
                }
            },
            # Center align all STT column values (column B, index 1) - chuyển từ cột C sang B
            {
                "repeatCell": {
                    "range": {
                        "sheetId": sheet_id,
                        "startRowIndex": 2,  # data starts at row 3 (0-based)
                        "startColumnIndex": 1,  # Chuyển từ 2 sang 1
                        "endColumnIndex": 2
                    },
                    "cell": {
                        "userEnteredFormat": {
                            "horizontalAlignment": "CENTER"
                        }
                    },
                    "fields": "userEnteredFormat.horizontalAlignment"
                }
            },
            # Căn phải cột Doanh thu, KHÔNG format tiền tệ (column H, index 7) - chuyển từ cột I sang H
            {
                "repeatCell": {
                    "range": {
                        "sheetId": sheet_id,
                        "startRowIndex": 2,
                        "startColumnIndex": 7,  # Chuyển từ 8 sang 7
                        "endColumnIndex": 8
                    },
                    "cell": {
                        "userEnteredFormat": {
                            "horizontalAlignment": "RIGHT"
                        }
                    },
                    "fields": "userEnteredFormat.horizontalAlignment"
                }
            },
            # Căn giữa cột Cluster (column I, index 8)
            {
                "repeatCell": {
                    "range": {
                        "sheetId": sheet_id,
                        "startRowIndex": 2,
                        "startColumnIndex": 8,  # Cột I (Cluster)
                        "endColumnIndex": 9
                    },
                    "cell": {
                        "userEnteredFormat": {
                            "horizontalAlignment": "CENTER"
                        }
                    },
                    "fields": "userEnteredFormat.horizontalAlignment"
                }
            }
        ]

        # Thêm style cho ô TỔNG CỘNG và tổng doanh thu
        if actual_rows > 0:
            format_requests.extend([
                # Thêm style cho ô TỔNG CỘNG (nền cam, chữ trắng, in đậm, căn giữa)
                {
                    "repeatCell": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": total_row - 1,  # -1 vì index dựa trên 0
                            "endRowIndex": total_row,
                            "startColumnIndex": 6,  # Cột G (Đã bán)
                            "endColumnIndex": 7
                        },
                        "cell": {
                            "userEnteredFormat": {
                                "backgroundColor": {"red": 1, "green": 0.6, "blue": 0.2},
                                "horizontalAlignment": "CENTER",
                                "textFormat": {"bold": True, "foregroundColor": {"red": 1, "green": 1, "blue": 1}}
                            }
                        },
                        "fields": "userEnteredFormat(backgroundColor,textFormat,horizontalAlignment)"
                    }
                },
                # Thêm style in đậm cho ô tổng doanh thu
                {
                    "repeatCell": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": total_row - 1,  # -1 vì index dựa trên 0
                            "endRowIndex": total_row,
                            "startColumnIndex": 7,  # Cột H (Doanh thu)
                            "endColumnIndex": 8
                        },
                        "cell": {
                            "userEnteredFormat": {
                                "horizontalAlignment": "RIGHT",
                                "textFormat": {"bold": True}
                            }
                        },
                        "fields": "userEnteredFormat(horizontalAlignment,textFormat)"
                    }
                }
            ])

        # Thiết lập kích thước cột
        dimension_requests = [
            {
                "updateDimensionProperties": {
                    "range": {
                        "sheetId": sheet_id,
                        "dimension": "COLUMNS",
                        "startIndex": 1,  # Cột B (STT)
                        "endIndex": 2
                    },
                    "properties": {
                        "pixelSize": 75  # Cột STT rộng 75 pixel
                    },
                    "fields": "pixelSize"
                }
            },
            {
                "updateDimensionProperties": {
                    "range": {
                        "sheetId": sheet_id,
                        "dimension": "COLUMNS",
                        "startIndex": 4,  # Cột E (Tên sản phẩm)
                        "endIndex": 5
                    },
                    "properties": {
                        "pixelSize": 880  # Cột Tên sản phẩm rộng 880 pixel
                    },
                    "fields": "pixelSize"
                }
            },
            {
                "updateDimensionProperties": {
                    "range": {
                        "sheetId": sheet_id,
                        "dimension": "COLUMNS",
                        "startIndex": 5,  # Cột F (Link sản phẩm)
                        "endIndex": 6
                    },
                    "properties": {
                        "pixelSize": 315  # Cột Link sản phẩm rộng 315 pixel
                    },
                    "fields": "pixelSize"
                }
            },
            {
                "updateDimensionProperties": {
                    "range": {
                        "sheetId": sheet_id,
                        "dimension": "COLUMNS",
                        "startIndex": 6,  # Cột G (Đã bán)
                        "endIndex": 7
                    },
                    "properties": {
                        "pixelSize": 100  # Cột Đã bán rộng 70 pixel (điều chỉnh từ 120 xuống 70)
                    },
                    "fields": "pixelSize"
                }
            },
            {
                "updateDimensionProperties": {
                    "range": {
                        "sheetId": sheet_id,
                        "dimension": "COLUMNS",
                        "startIndex": 7,  # Cột H (Doanh thu)
                        "endIndex": 8
                    },
                    "properties": {
                        "pixelSize": 160  # Cột Doanh thu rộng 160 pixel
                    },
                    "fields": "pixelSize"
                }
            },
            {
                "updateDimensionProperties": {
                    "range": {
                        "sheetId": sheet_id,
                        "dimension": "COLUMNS",
                        "startIndex": 8,  # Cột I (Cluster)
                        "endIndex": 9
                    },
                    "properties": {
                        "pixelSize": 80  # Cột Cluster rộng 80 pixel (điều chỉnh từ 120 xuống 80)
                    },
                    "fields": "pixelSize"
                }
            }
        ]

        # Tách các request thành hai phần để tránh lỗi
        try:
            # is_first_update đã được xác định ở trên

            # Chỉ ghi header trong lần cập nhật đầu tiên
            if is_first_update:
                self.log(f"Lần cập nhật đầu tiên cho sheet {sheet}, ghi cả header và dữ liệu")
                await self.execute_with_retry(
                    self.service.spreadsheets().values().update(
                        spreadsheetId=self.spreadsheet_id,
                        range=f"{sheet}!B2:I2",  # Mở rộng phạm vi để bao gồm cột Cluster (I)
                        valueInputOption="USER_ENTERED",
                        body={"values": [HEADER]}
                    ),
                    "Lỗi khi ghi header"
                )
                # Đánh dấu đã cập nhật sheet này
                self.first_sheet_update[sheet] = True

                # Lưu số lượng dòng hiện tại cho lần cập nhật tiếp theo
                if actual_rows > 0:
                    self.last_row_count[sheet] = len(values)
            else:
                self.log(f"Lần cập nhật tiếp theo cho sheet {sheet}, chỉ ghi dữ liệu (bỏ qua header)")

            # Ghi dữ liệu
            if actual_rows > 0:
                # Trong lần cập nhật đầu tiên, cập nhật tất cả các cột
                if is_first_update:
                    # Thêm một cột trống cho Cluster vào mỗi dòng dữ liệu
                    for row in values:
                        row.append("")  # Thêm cột trống cho Cluster

                    await self.execute_with_retry(
                        self.service.spreadsheets().values().update(
                            spreadsheetId=self.spreadsheet_id,
                            range=f"{sheet}!B3:I{last_row}",  # Mở rộng phạm vi để bao gồm cột Cluster (I)
                            valueInputOption="USER_ENTERED",
                            body={"values": values}
                        ),
                        "Lỗi khi ghi dữ liệu chính"
                    )

                    # Thêm công thức VLOOKUP cho cột Shop ID, công thức URL cho cột Link sản phẩm, và công thức VLOOKUP cho cột Cluster
                    formulas_shop_id = []
                    formulas_link = []
                    formulas_cluster = []

                    for i in range(3, last_row+1):  # Chỉ tạo công thức cho số dòng thực tế có dữ liệu
                        formulas_shop_id.append([f"=VLOOKUP(D{i},'Deal list'!$C$4:$V,4,FALSE)"])  # Chuyển từ E sang D
                        formulas_link.append([f"=\"https://shopee.vn/a-i.\"&C{i}&\".\"&D{i}"])  # Chuyển từ D,E sang C,D
                        formulas_cluster.append([f"=VLOOKUP(D{i},'Deal list'!$C$4:$V,7,FALSE)"])  # Công thức VLOOKUP cho cột Cluster

                    # Cập nhật công thức cho cột Shop ID (C)
                    await self.execute_with_retry(
                        self.service.spreadsheets().values().update(
                            spreadsheetId=self.spreadsheet_id,
                            range=f"{sheet}!C3:C{last_row}",  # Chuyển từ D sang C
                            valueInputOption="USER_ENTERED",
                            body={"values": formulas_shop_id}
                        ),
                        "Lỗi khi cập nhật công thức Shop ID"
                    )

                    # Cập nhật công thức cho cột Link sản phẩm (F)
                    await self.execute_with_retry(
                        self.service.spreadsheets().values().update(
                            spreadsheetId=self.spreadsheet_id,
                            range=f"{sheet}!F3:F{last_row}",  # Chuyển từ G sang F
                            valueInputOption="USER_ENTERED",
                            body={"values": formulas_link}
                        ),
                        "Lỗi khi cập nhật công thức Link sản phẩm"
                    )

                    # Cập nhật công thức cho cột Cluster (I)
                    await self.execute_with_retry(
                        self.service.spreadsheets().values().update(
                            spreadsheetId=self.spreadsheet_id,
                            range=f"{sheet}!I3:I{last_row}",
                            valueInputOption="USER_ENTERED",
                            body={"values": formulas_cluster}
                        ),
                        "Lỗi khi cập nhật công thức Cluster"
                    )


                else:
                    # Trong các lần cập nhật tiếp theo, chỉ cập nhật các cột dữ liệu thực tế (STT, Item ID, Tên sản phẩm, Đã bán, Doanh thu)
                    self.log("Chỉ cập nhật các cột dữ liệu thực tế, bỏ qua các cột có công thức")

                    # Cập nhật từng cột riêng biệt thay vì cố gắng cập nhật nhiều vùng không liên tục

                    # 1. Cập nhật cột STT (B) chỉ khi số lượng dòng thay đổi
                    current_row_count = len(values)

                    # Kiểm tra xem có cần cập nhật cột STT không
                    update_stt = False

                    # Nếu đây là lần đầu tiên cập nhật sheet này hoặc số lượng dòng đã thay đổi
                    if sheet not in self.last_row_count or self.last_row_count[sheet] != current_row_count:
                        update_stt = True
                        self.log(f"Số lượng dòng đã thay đổi (trước: {self.last_row_count.get(sheet, 'chưa có')}, hiện tại: {current_row_count}), cập nhật cột STT")
                    else:
                        self.log(f"Số lượng dòng không thay đổi ({current_row_count}), bỏ qua cập nhật cột STT")

                    # Lưu số lượng dòng hiện tại cho lần cập nhật tiếp theo
                    self.last_row_count[sheet] = current_row_count

                    # Chỉ cập nhật cột STT nếu cần thiết
                    if update_stt:
                        stt_values = [[row[0]] for row in values]
                        await self.execute_with_retry(
                            self.service.spreadsheets().values().update(
                                spreadsheetId=self.spreadsheet_id,
                                range=f"{sheet}!B3:B{last_row}",
                                valueInputOption="USER_ENTERED",
                                body={"values": stt_values}
                            ),
                            "Lỗi khi cập nhật cột STT"
                        )

                    # 2. Cập nhật cột Item ID (D)
                    item_id_values = [[row[2]] for row in values]
                    await self.execute_with_retry(
                        self.service.spreadsheets().values().update(
                            spreadsheetId=self.spreadsheet_id,
                            range=f"{sheet}!D3:D{last_row}",
                            valueInputOption="USER_ENTERED",
                            body={"values": item_id_values}
                        ),
                        "Lỗi khi cập nhật cột Item ID"
                    )



                    # 3. Cập nhật cột Tên sản phẩm (E)
                    name_values = [[row[3]] for row in values]
                    await self.execute_with_retry(
                        self.service.spreadsheets().values().update(
                            spreadsheetId=self.spreadsheet_id,
                            range=f"{sheet}!E3:E{last_row}",
                            valueInputOption="USER_ENTERED",
                            body={"values": name_values}
                        ),
                        "Lỗi khi cập nhật cột Tên sản phẩm"
                    )

                    # 4. Cập nhật cột Đã bán (G)
                    sold_values = [[row[5]] for row in values]
                    await self.execute_with_retry(
                        self.service.spreadsheets().values().update(
                            spreadsheetId=self.spreadsheet_id,
                            range=f"{sheet}!G3:G{last_row}",
                            valueInputOption="USER_ENTERED",
                            body={"values": sold_values}
                        ),
                        "Lỗi khi cập nhật cột Đã bán"
                    )

                    # 5. Cập nhật cột Doanh thu (H)
                    revenue_values = [[row[6]] for row in values]
                    await self.execute_with_retry(
                        self.service.spreadsheets().values().update(
                            spreadsheetId=self.spreadsheet_id,
                            range=f"{sheet}!H3:H{last_row}",
                            valueInputOption="USER_ENTERED",
                            body={"values": revenue_values}
                        ),
                        "Lỗi khi cập nhật cột Doanh thu"
                    )

                # Thêm dòng TỔNG CỘNG
                try:
                    # Tính tổng doanh thu từ dữ liệu
                    total = sum([float((row[6].replace("₫", "").replace(".", "").replace(",", ".")).strip()) for row in values])
                    # Format tổng doanh thu theo định dạng dashboard để dễ đối chiếu
                    total_formatted = f"₫ {total:,.2f}".replace(",", "X").replace(".", ",").replace("X", ".")

                    # Thêm dòng tổng cộng vào sheet
                    await self.execute_with_retry(
                        self.service.spreadsheets().values().update(
                            spreadsheetId=self.spreadsheet_id,
                            range=f"{sheet}!G{total_row}:H{total_row}",
                            valueInputOption="USER_ENTERED",
                            body={"values": [["TỔNG CỘNG", total_formatted]]}
                        ),
                        "Lỗi khi thêm dòng tổng cộng"
                    )
                except Exception as e:
                    self.log(f"Lỗi khi thêm dòng tổng cộng: {e}")



            # Bỏ qua formatting để tăng tốc độ - chỉ cần dữ liệu
            # ULTRA FAST MODE: Skip formatting để tránh timeout
            if is_first_update and not self._ultra_fast_mode:
                # Gửi tất cả formatting trong 1 batch duy nhất để tối ưu
                all_requests = format_requests + dimension_requests
                if all_requests:
                    format_success = await self.execute_with_retry(
                        self.service.spreadsheets().batchUpdate(
                            spreadsheetId=self.spreadsheet_id,
                            body={"requests": all_requests}
                        ),
                        "Lỗi khi áp dụng định dạng và kích thước cột",
                        max_attempts=2,  # Fail fast để tránh timeout
                        initial_delay=1
                    )
                    if format_success:
                        self.log(f"✅ Đã áp dụng {len(format_requests)} định dạng + {len(dimension_requests)} kích thước cột trong 1 batch")
                    else:
                        self.log("⚠️ Formatting timeout, bỏ qua để tiếp tục")
            else:
                if self._ultra_fast_mode:
                    self.log("⚡ ULTRA FAST MODE: Bỏ qua formatting để tránh timeout")
                else:
                    pass  # Bỏ log để giảm clutter

            # Tạo bảng tổng hợp Cluster
            if actual_rows > 0:
                # Lấy số lượng sản phẩm từ top_selector
                selected_top = self.top_selector.currentText()
                num_products = int(selected_top.replace("Top ", ""))

                # Kiểm tra xem đây có phải là lần cập nhật đầu tiên hoặc có thay đổi về số lượng sản phẩm không
                update_cluster_table = is_first_update

                # Nếu không phải lần đầu, kiểm tra xem có thay đổi về số lượng sản phẩm không
                if not update_cluster_table and hasattr(self, 'last_num_products') and self.last_num_products != num_products:
                    update_cluster_table = True
                    self.log(f"Phát hiện thay đổi số lượng sản phẩm (từ {self.last_num_products} sang {num_products}), sẽ cập nhật bảng tổng hợp Cluster")

                # Lưu lại số lượng sản phẩm hiện tại cho lần so sánh tiếp theo
                self.last_num_products = num_products

                # Không xóa bảng tổng hợp Cluster cũ, chỉ cập nhật lại công thức khi cần thiết
                # Lần đầu tiên đã xử lý xóa ở trên, các lần sau không cần xóa

                await self.create_cluster_summary_table(sheet, sheet_id, last_row, update_cluster_table, num_products)

        except Exception as e:
            self.log(f"❌ Lỗi khi cập nhật Google Sheets: {e}")
            import traceback
            self.log(traceback.format_exc())



    async def get_sheet_id(self, sheet_name):
        """
        Lấy sheet ID từ tên sheet
        """
        try:
            sheet_metadata = await self.execute_with_retry(
                self.service.spreadsheets().get(spreadsheetId=self.spreadsheet_id),
                "Lỗi khi lấy metadata sheet"
            )

            for sheet in sheet_metadata.get('sheets', []):
                if sheet['properties']['title'] == sheet_name:
                    return sheet['properties']['sheetId']

            return 0  # Default sheet ID nếu không tìm thấy

        except Exception as e:
            self.log(f"❌ Lỗi khi lấy sheet ID: {e}")
            return 0

    async def create_cluster_summary_table(self, sheet, sheet_id, last_row, update_table=True, num_products=None):
        """
        Tạo bảng tổng hợp theo Cluster với các công thức tính toán

        Args:
            sheet: Tên sheet
            sheet_id: ID của sheet
            last_row: Dòng cuối cùng có dữ liệu trong bảng chính
            update_table: Có cập nhật bảng hay không (True cho lần đầu hoặc khi thay đổi số lượng sản phẩm)
            num_products: Số lượng sản phẩm đang hiển thị (top)
        """
        try:
            self.log("Đang xử lý bảng tổng hợp Cluster...")

            # Kiểm tra xem bảng tổng hợp đã tồn tại chưa
            try:
                # Thêm cơ chế retry cho API request
                max_retries = 3
                retry_delay = 5  # Đợi 5 giây trước khi thử lại

                for retry_count in range(max_retries):
                    try:
                        response = self.service.spreadsheets().values().get(
                            spreadsheetId=self.spreadsheet_id,
                            range=f"{sheet}!K2:K2"
                        ).execute()

                        if 'values' in response and response['values'][0][0] == "Cluster":
                            self.log("Bảng tổng hợp Cluster đã tồn tại")
                            # Nếu không cần cập nhật bảng và bảng đã tồn tại, chỉ cần cập nhật công thức
                            if not update_table:
                                self.log("Không cần cập nhật bảng tổng hợp Cluster, chỉ cập nhật công thức nếu cần")
                        else:
                            # Nếu không tìm thấy header "Cluster", cần tạo mới bảng
                            self.log("Không tìm thấy bảng tổng hợp Cluster, sẽ tạo mới")
                            update_table = True

                        # Nếu thành công, thoát khỏi vòng lặp retry
                        break

                    except TimeoutError as e:
                        if retry_count < max_retries - 1:
                            self.log(f"API timeout khi kiểm tra bảng Cluster, thử lại lần {retry_count + 1}/{max_retries} sau {retry_delay} giây...")
                            await asyncio.sleep(retry_delay)
                            # Tăng thời gian chờ cho lần thử tiếp theo
                            retry_delay *= 2
                        else:
                            # Nếu đã hết số lần thử, ném lại exception
                            raise e
                    except Exception as e:
                        if retry_count < max_retries - 1:
                            self.log(f"Lỗi khi kiểm tra bảng Cluster: {e}, thử lại lần {retry_count + 1}/{max_retries}...")
                            await asyncio.sleep(retry_delay)
                            retry_delay *= 2
                        else:
                            # Nếu đã hết số lần thử, ném lại exception
                            raise e

            except Exception:
                # Nếu có lỗi khi kiểm tra, cần tạo mới bảng
                self.log("Lỗi khi kiểm tra bảng tổng hợp Cluster, sẽ tạo mới")
                update_table = True

            # Sử dụng danh sách cluster cố định
            clusters = CLUSTERS
            self.log(f"Sử dụng {len(clusters)} cluster cố định: {', '.join(clusters)}")

            # Nếu cần tạo mới hoặc cập nhật bảng
            if update_table:
                self.log("Đang tạo/cập nhật bảng tổng hợp Cluster...")

                # Thiết lập header cho bảng tổng hợp
                cluster_header = ["Cluster", "SKU Sold", "Revenue", "Prop"]
                await self.execute_with_retry(
                    self.service.spreadsheets().values().update(
                        spreadsheetId=self.spreadsheet_id,
                        range=f"{sheet}!K2:N2",
                        valueInputOption="USER_ENTERED",
                        body={"values": [cluster_header]}
                    ),
                    "Lỗi khi cập nhật header Cluster"
                )

                # Định dạng header giống với bảng chính
                format_requests = [
                    {
                        "repeatCell": {
                            "range": {
                                "sheetId": sheet_id,
                                "startRowIndex": 1,
                                "endRowIndex": 2,
                                "startColumnIndex": 10,  # Cột K
                                "endColumnIndex": 14     # Đến cột N
                            },
                            "cell": {
                                "userEnteredFormat": {
                                    "backgroundColor": {"red": 1, "green": 0.6, "blue": 0.2},
                                    "horizontalAlignment": "CENTER",
                                    "textFormat": {"bold": True, "foregroundColor": {"red": 1, "green": 1, "blue": 1}}
                                }
                            },
                            "fields": "userEnteredFormat(backgroundColor,textFormat,horizontalAlignment)"
                        }
                    }
                ]

                # Thiết lập kích thước cột cho bảng tổng hợp
                dimension_requests = [
                    {
                        "updateDimensionProperties": {
                            "range": {
                                "sheetId": sheet_id,
                                "dimension": "COLUMNS",
                                "startIndex": 10,  # Cột K (Cluster)
                                "endIndex": 11
                            },
                            "properties": {
                                "pixelSize": 100  # Cột Cluster rộng 100 pixel
                            },
                            "fields": "pixelSize"
                        }
                    },
                    {
                        "updateDimensionProperties": {
                            "range": {
                                "sheetId": sheet_id,
                                "dimension": "COLUMNS",
                                "startIndex": 11,  # Cột L (SKU Sold)
                                "endIndex": 12
                            },
                            "properties": {
                                "pixelSize": 100  # Cột SKU Sold rộng 100 pixel
                            },
                            "fields": "pixelSize"
                        }
                    },
                    {
                        "updateDimensionProperties": {
                            "range": {
                                "sheetId": sheet_id,
                                "dimension": "COLUMNS",
                                "startIndex": 12,  # Cột M (Revenue)
                                "endIndex": 13
                            },
                            "properties": {
                                "pixelSize": 160  # Cột Revenue rộng 160 pixel
                            },
                            "fields": "pixelSize"
                        }
                    },
                    {
                        "updateDimensionProperties": {
                            "range": {
                                "sheetId": sheet_id,
                                "dimension": "COLUMNS",
                                "startIndex": 13,  # Cột N (Prop)
                                "endIndex": 14
                            },
                            "properties": {
                                "pixelSize": 80  # Cột Prop rộng 80 pixel
                            },
                            "fields": "pixelSize"
                        }
                    }
                ]

                # Bỏ qua cluster formatting để tăng tốc độ
                if False:  # Tạm thời disable
                    all_cluster_requests = format_requests + dimension_requests
                    await self.execute_with_retry(
                        self.service.spreadsheets().batchUpdate(
                            spreadsheetId=self.spreadsheet_id,
                            body={"requests": all_cluster_requests}
                        ),
                        "Lỗi khi định dạng header và kích thước cột Cluster"
                    )
                    self.log("✅ Đã định dạng header và kích thước cột Cluster trong 1 batch")
                else:
                    pass  # Bỏ log để giảm clutter

            # Tạo dữ liệu cho bảng tổng hợp
            cluster_data = []
            for i, cluster in enumerate(clusters):
                row_num = i + 3  # Bắt đầu từ dòng 3

                # Sử dụng num_products nếu được cung cấp, nếu không thì sử dụng last_row
                end_row = num_products + 2 if num_products else last_row

                # Công thức cho SKU Sold: =SUMIF($I$3:$I$502,K3,$G$3:$G$502)
                sku_sold_formula = f"=SUMIF($I$3:$I${end_row},K{row_num},$G$3:$G${end_row})"

                # Công thức cho Revenue: =SUMPRODUCT(($I$3:$I$502=K3)*VALUE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE($H$3:$H$502, "₫", ""), ".", ""), ",", ".")))
                revenue_formula = f"=SUMPRODUCT(($I$3:$I${end_row}=K{row_num})*VALUE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE($H$3:$H${end_row}, \"₫\", \"\"), \".\", \"\"), \",\", \".\")))"

                # Công thức cho Prop: =M3/SUM($M$3:$M$8)
                # Chú ý: Chúng ta cần cập nhật phạm vi tổng sau khi biết có bao nhiêu cluster

                cluster_data.append([cluster, sku_sold_formula, revenue_formula, ""])  # Để trống cột Prop, sẽ cập nhật sau

            # Nếu cần tạo mới bảng
            if update_table:
                # ULTRA FAST MODE: Skip header checking để tránh timeout
                try:
                    if self._ultra_fast_mode:
                        self.log("⚡ ULTRA FAST MODE: Bỏ qua kiểm tra header, tạo mới luôn")
                        response = None  # Force tạo mới
                    else:
                        response = await self.execute_with_retry(
                            self.service.spreadsheets().values().get(
                                spreadsheetId=self.spreadsheet_id,
                                range=f"{sheet}!K2:N2"
                            ),
                            "Lỗi khi kiểm tra header bảng tổng hợp",
                            max_attempts=2,  # Fail fast
                            initial_delay=1
                        )

                    if not response or 'values' not in response or response['values'][0][0] != "Cluster":
                        # Nếu không có header hoặc header không đúng, tạo mới
                        self.log("Tạo mới header cho bảng tổng hợp Cluster")
                        cluster_header = ["Cluster", "SKU Sold", "Revenue", "Prop"]
                        await self.execute_with_retry(
                            self.service.spreadsheets().values().update(
                                spreadsheetId=self.spreadsheet_id,
                                range=f"{sheet}!K2:N2",
                                valueInputOption="USER_ENTERED",
                                body={"values": [cluster_header]}
                            ),
                            "Lỗi khi tạo header bảng tổng hợp"
                        )
                    else:
                        self.log("Header bảng tổng hợp Cluster đã tồn tại, không cần tạo lại")
                except Exception:
                    # Nếu có lỗi khi kiểm tra, tạo mới header
                    self.log("Lỗi khi kiểm tra header bảng tổng hợp Cluster, tạo mới")
                    cluster_header = ["Cluster", "SKU Sold", "Revenue", "Prop"]
                    await self.execute_with_retry(
                        self.service.spreadsheets().values().update(
                            spreadsheetId=self.spreadsheet_id,
                            range=f"{sheet}!K2:N2",
                            valueInputOption="USER_ENTERED",
                            body={"values": [cluster_header]}
                        ),
                        "Lỗi khi tạo header bảng tổng hợp (lần 2)"
                    )

                # Cập nhật toàn bộ dữ liệu cho bảng tổng hợp
                await self.execute_with_retry(
                    self.service.spreadsheets().values().update(
                        spreadsheetId=self.spreadsheet_id,
                        range=f"{sheet}!K3:N{2 + len(cluster_data)}",
                        valueInputOption="USER_ENTERED",
                        body={"values": cluster_data}
                    ),
                    "Lỗi khi cập nhật dữ liệu bảng tổng hợp Cluster"
                )

                # Cập nhật công thức cho cột Prop
                prop_formulas = []
                last_cluster_row = 2 + len(cluster_data)
                for i in range(len(cluster_data)):
                    row_num = i + 3
                    prop_formula = f"=M{row_num}/SUM($M$3:$M${last_cluster_row})"
                    prop_formulas.append([prop_formula])

                await self.execute_with_retry(
                    self.service.spreadsheets().values().update(
                        spreadsheetId=self.spreadsheet_id,
                        range=f"{sheet}!N3:N{last_cluster_row}",
                        valueInputOption="USER_ENTERED",
                        body={"values": prop_formulas}
                    ),
                    "Lỗi khi cập nhật công thức Prop"
                )
            else:
                # Nếu không cần tạo mới, chỉ cập nhật công thức khi có thay đổi số lượng sản phẩm
                # Luôn cập nhật dữ liệu khi sử dụng danh sách cluster cố định
                self.log("Cập nhật dữ liệu Cluster cố định...")

                # Cập nhật toàn bộ dữ liệu
                await self.execute_with_retry(
                    self.service.spreadsheets().values().update(
                        spreadsheetId=self.spreadsheet_id,
                        range=f"{sheet}!K3:M{2 + len(cluster_data)}",
                        valueInputOption="USER_ENTERED",
                        body={"values": [[row[0], row[1], row[2]] for row in cluster_data]}
                    ),
                    "Lỗi khi cập nhật danh sách cluster"
                )

                # Cập nhật công thức cho cột Prop
                prop_formulas = []
                last_cluster_row = 2 + len(cluster_data)
                for i in range(len(cluster_data)):
                    row_num = i + 3
                    prop_formula = f"=M{row_num}/SUM($M$3:$M${last_cluster_row})"
                    prop_formulas.append([prop_formula])

                await self.execute_with_retry(
                    self.service.spreadsheets().values().update(
                        spreadsheetId=self.spreadsheet_id,
                        range=f"{sheet}!N3:N{last_cluster_row}",
                        valueInputOption="USER_ENTERED",
                        body={"values": prop_formulas}
                    ),
                    "Lỗi khi cập nhật công thức Prop (thay đổi cluster)"
                )

            # Nếu cần tạo mới hoặc cập nhật bảng, thêm định dạng
            if update_table:
                # Định dạng cột Prop dưới dạng phần trăm
                format_prop_request = {
                    "repeatCell": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": 2,
                            "endRowIndex": 2 + len(cluster_data),
                            "startColumnIndex": 13,  # Cột N (Prop)
                            "endColumnIndex": 14
                        },
                        "cell": {
                            "userEnteredFormat": {
                                "numberFormat": {
                                    "type": "PERCENT",
                                    "pattern": "0%"
                                },
                                "horizontalAlignment": "RIGHT"
                            }
                        },
                        "fields": "userEnteredFormat(numberFormat,horizontalAlignment)"
                    }
                }

                # Định dạng cột Revenue căn phải
                format_revenue_request = {
                    "repeatCell": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": 2,
                            "endRowIndex": 2 + len(cluster_data),
                            "startColumnIndex": 12,  # Cột M (Revenue)
                            "endColumnIndex": 13
                        },
                        "cell": {
                            "userEnteredFormat": {
                                "horizontalAlignment": "RIGHT"
                            }
                        },
                        "fields": "userEnteredFormat.horizontalAlignment"
                    }
                }

                # Định dạng cột SKU Sold căn phải
                format_sku_sold_request = {
                    "repeatCell": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": 2,
                            "endRowIndex": 2 + len(cluster_data),
                            "startColumnIndex": 11,  # Cột L (SKU Sold)
                            "endColumnIndex": 12
                        },
                        "cell": {
                            "userEnteredFormat": {
                                "horizontalAlignment": "RIGHT"
                            }
                        },
                        "fields": "userEnteredFormat.horizontalAlignment"
                    }
                }

                # Định dạng cột Cluster căn giữa
                format_cluster_request = {
                    "repeatCell": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": 2,
                            "endRowIndex": 2 + len(cluster_data),
                            "startColumnIndex": 10,  # Cột K (Cluster)
                            "endColumnIndex": 11
                        },
                        "cell": {
                            "userEnteredFormat": {
                                "horizontalAlignment": "CENTER"
                            }
                        },
                        "fields": "userEnteredFormat.horizontalAlignment"
                    }
                }

                # Bỏ qua formatting để tăng tốc độ
                if False:  # Tạm thời disable
                    all_format_requests = [format_prop_request, format_revenue_request, format_sku_sold_request, format_cluster_request]
                    await self.execute_with_retry(
                        self.service.spreadsheets().batchUpdate(
                            spreadsheetId=self.spreadsheet_id,
                            body={"requests": all_format_requests}
                        ),
                        "Lỗi khi định dạng các cột cluster"
                    )
                    self.log("✅ Đã định dạng tất cả cột cluster trong 1 batch")
                else:
                    pass  # Bỏ log để giảm clutter

                self.log(f"✅ Đã tạo/cập nhật bảng tổng hợp Cluster với {len(cluster_data)} cluster")
            else:
                self.log(f"✅ Đã cập nhật công thức cho bảng tổng hợp Cluster với {len(cluster_data)} cluster")

            # Cập nhật Historical Data với logic tối ưu mới
            if not self._emergency_mode:
                self.log("🔄 Cập nhật dữ liệu vào sheet Historical Data...")
                await self.update_historical_data(cluster_data)
                self.log("✅ Đã cập nhật dữ liệu lịch sử thành công")
            else:
                self.log("🚨 EMERGENCY MODE: Bỏ qua Historical Data để tránh timeout")

        except Exception as e:
            self.log(f"❌ Lỗi khi tạo bảng tổng hợp Cluster: {e}")
            import traceback
            self.log(traceback.format_exc())

    async def update_historical_data(self, clusters_data):
        """
        Tạo sheet Historical Data với Timeline tracking và biểu đồ phân tích theo khung giờ

        Args:
            clusters_data: Dữ liệu các cluster vừa cập nhật
        """
        try:
            # Lấy thông tin sheet từ cache để tránh timeout
            sheet_metadata = await self.get_cached_sheet_metadata()

            # Tìm và xử lý các sheet Historical Data (chặt chẽ hơn để tránh false positive)
            historical_sheets = []
            for sheet in sheet_metadata.get('sheets', []):
                title = sheet['properties']['title']
                # Chỉ match chính xác hoặc có suffix conflict/copy
                if (title == 'Historical Data' or
                    title.startswith('Historical Data_conflict') or
                    title.startswith('Historical Data_copy') or
                    title.startswith('Copy of Historical Data')):
                    historical_sheets.append({
                        'id': sheet['properties']['sheetId'],
                        'title': title
                    })
                    # Log ít hơn để giảm clutter
                    pass

            # Xử lý duplicate sheets với logging chi tiết
            if len(historical_sheets) > 1:
                self.log(f"🚨 PHÁT HIỆN {len(historical_sheets)} SHEET DUPLICATE:")
                for sheet_info in historical_sheets:
                    self.log(f"   - '{sheet_info['title']}' (ID: {sheet_info['id']})")
                self.log("🧹 Bắt đầu force cleanup...")
                historical_sheet_id = await self.force_cleanup_all_historical_sheets(historical_sheets)
                if historical_sheet_id:
                    self.log(f"✅ Force cleanup thành công, sheet ID: {historical_sheet_id}")
                else:
                    self.log("❌ Force cleanup thất bại")
                    return
            elif len(historical_sheets) == 1:
                sheet_info = historical_sheets[0]
                self.log(f"📋 Tìm thấy 1 sheet: '{sheet_info['title']}' (ID: {sheet_info['id']})")
                if sheet_info['title'] != 'Historical Data':
                    self.log("🔄 Cần rename sheet về tên chuẩn...")
                    await self.rename_sheet_to_historical_data(sheet_info['id'])
                historical_sheet_id = sheet_info['id']
                self.log(f"✅ Sử dụng sheet ID: {historical_sheet_id}")
            else:
                self.log("📝 Không tìm thấy sheet nào, tạo mới...")
                historical_sheet_id = await self.create_historical_sheet_optimized()
                if not historical_sheet_id:
                    self.log("❌ Không thể tạo sheet mới")
                    return
                else:
                    self.log(f"✅ Đã tạo sheet mới ID: {historical_sheet_id}")

            # Final check: Đảm bảo không có duplicate sheet nào được tạo
            await self.final_check_no_duplicates()

            # Cập nhật dữ liệu và biểu đồ trong một batch
            await self.update_historical_data_optimized(historical_sheet_id, clusters_data)

        except Exception as e:
            self.log(f"❌ Lỗi khi cập nhật Historical Data: {e}")
            import traceback
            self.log(traceback.format_exc())

    async def cleanup_duplicate_historical_sheets(self, historical_sheets):
        """
        Dọn dẹp các sheet Historical Data duplicate, giữ lại sheet có dữ liệu nhiều nhất
        """
        try:
            self.log("🧹 Đang dọn dẹp các sheet Historical Data duplicate...")

            # Tìm sheet có dữ liệu nhiều nhất
            best_sheet = None
            max_data_rows = 0

            for sheet_info in historical_sheets:
                sheet_id = sheet_info['id']
                sheet_title = sheet_info['title']

                try:
                    # Kiểm tra số dòng dữ liệu trong sheet này
                    response = await self.execute_with_retry(
                        self.service.spreadsheets().values().get(
                            spreadsheetId=self.spreadsheet_id,
                            range=f"'{sheet_title}'!A2:A29"
                        ),
                        f"Lỗi khi kiểm tra dữ liệu sheet {sheet_title}"
                    )

                    data_rows = 0
                    if response and 'values' in response:
                        for row in response['values']:
                            if row and row[0].strip():  # Có timestamp
                                data_rows += 1

                    self.log(f"Sheet '{sheet_title}' (ID: {sheet_id}) có {data_rows} dòng dữ liệu")

                    if data_rows > max_data_rows:
                        max_data_rows = data_rows
                        best_sheet = sheet_info

                except Exception as e:
                    self.log(f"⚠️ Không thể kiểm tra sheet '{sheet_title}': {e}")
                    # Nếu không kiểm tra được, coi như sheet này có 0 dòng dữ liệu
                    if best_sheet is None:
                        best_sheet = sheet_info

            if not best_sheet:
                self.log("❌ Không thể xác định sheet tốt nhất, tạo sheet mới")
                return await self.create_historical_sheet_optimized()

            # Xóa các sheet duplicate khác
            sheets_to_delete = [s for s in historical_sheets if s['id'] != best_sheet['id']]

            if sheets_to_delete:
                delete_requests = []
                for sheet_info in sheets_to_delete:
                    delete_requests.append({
                        'deleteSheet': {
                            'sheetId': sheet_info['id']
                        }
                    })
                    self.log(f"🗑️ Xóa sheet duplicate: '{sheet_info['title']}' (ID: {sheet_info['id']})")

                await self.execute_with_retry(
                    self.service.spreadsheets().batchUpdate(
                        spreadsheetId=self.spreadsheet_id,
                        body={'requests': delete_requests}
                    ),
                    "Lỗi khi xóa sheet duplicate"
                )

                self.log(f"✅ Đã xóa {len(sheets_to_delete)} sheet duplicate")

            # Đổi tên sheet tốt nhất thành "Historical Data" nếu cần
            if best_sheet['title'] != 'Historical Data':
                rename_request = {
                    'updateSheetProperties': {
                        'properties': {
                            'sheetId': best_sheet['id'],
                            'title': 'Historical Data'
                        },
                        'fields': 'title'
                    }
                }

                await self.execute_with_retry(
                    self.service.spreadsheets().batchUpdate(
                        spreadsheetId=self.spreadsheet_id,
                        body={'requests': [rename_request]}
                    ),
                    "Lỗi khi đổi tên sheet"
                )

                self.log(f"✅ Đã đổi tên sheet '{best_sheet['title']}' thành 'Historical Data'")

            self.log(f"✅ Sử dụng sheet Historical Data (ID: {best_sheet['id']}) với {max_data_rows} dòng dữ liệu")
            return best_sheet['id']

        except Exception as e:
            self.log(f"❌ Lỗi khi dọn dẹp duplicate sheets: {e}")
            # Fallback: tạo sheet mới
            return await self.create_historical_sheet_optimized()

    async def force_cleanup_all_historical_sheets(self, historical_sheets):
        """
        FORCE cleanup: Xóa TẤT CẢ sheet Historical Data và tạo mới 1 sheet duy nhất
        """
        try:
            # Xóa TẤT CẢ sheet Historical Data
            delete_requests = []
            for sheet_info in historical_sheets:
                delete_requests.append({
                    'deleteSheet': {
                        'sheetId': sheet_info['id']
                    }
                })

            if delete_requests:
                await self.execute_with_retry(
                    self.service.spreadsheets().batchUpdate(
                        spreadsheetId=self.spreadsheet_id,
                        body={'requests': delete_requests}
                    ),
                    "Lỗi khi xóa sheet duplicate"
                )

            # Tạo 1 sheet mới duy nhất
            historical_sheet_id = await self.create_historical_sheet_optimized()
            return historical_sheet_id

        except Exception as e:
            self.log(f"❌ Lỗi khi force cleanup: {e}")
            # Fallback: tạo sheet mới
            return await self.create_historical_sheet_optimized()

    async def rename_sheet_to_historical_data(self, sheet_id):
        """
        Đổi tên sheet thành "Historical Data"
        """
        try:
            rename_request = {
                'updateSheetProperties': {
                    'properties': {
                        'sheetId': sheet_id,
                        'title': 'Historical Data'
                    },
                    'fields': 'title'
                }
            }

            await self.execute_with_retry(
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': [rename_request]}
                ),
                "Lỗi khi đổi tên sheet"
            )

            # Bỏ log để giảm clutter

        except Exception as e:
            self.log(f"❌ Lỗi khi đổi tên sheet: {e}")

    async def final_check_no_duplicates(self):
        """
        Final check: Đảm bảo không có duplicate Historical Data sheet nào
        """
        try:
            sheet_metadata = await self.execute_with_retry(
                self.service.spreadsheets().get(spreadsheetId=self.spreadsheet_id),
                "Lỗi khi final check duplicate sheets"
            )

            historical_count = 0
            for sheet in sheet_metadata.get('sheets', []):
                title = sheet['properties']['title']
                if (title == 'Historical Data' or
                    title.startswith('Historical Data_conflict') or
                    title.startswith('Historical Data_copy') or
                    title.startswith('Copy of Historical Data')):
                    historical_count += 1

            if historical_count > 1:
                self.log(f"⚠️ Vẫn có {historical_count} sheet duplicate!")
            # Bỏ log success để giảm clutter

        except Exception as e:
            self.log(f"❌ Lỗi khi final check: {e}")

    async def create_historical_sheet_optimized(self):
        """
        Tối ưu: Tạo Historical Data sheet với header và layout cơ bản trong một lần - ENHANCED DUPLICATE PROTECTION
        """
        try:
            self.log("🚀 Tạo mới sheet Historical Data với duplicate protection...")

            # Kiểm tra lần cuối trước khi tạo
            sheet_metadata = await self.get_cached_sheet_metadata(force_refresh=True)
            if sheet_metadata:
                for sheet in sheet_metadata.get('sheets', []):
                    if sheet['properties']['title'] == 'Historical Data':
                        existing_id = sheet['properties']['sheetId']
                        self.log(f"⚠️ Sheet Historical Data đã tồn tại (ID: {existing_id}), sử dụng sheet này")
                        return existing_id

            # Tạo sheet với tên unique để tránh conflict
            import time
            timestamp = int(time.time())
            temp_name = f"Historical_Data_Creating_{timestamp}"

            self.log(f"📝 Tạo sheet tạm thời: {temp_name}")

            # ULTRA FAST MODE: Minimal sheet creation
            requests = []

            # 1. Tạo sheet với tên tạm thời - MINIMAL CONFIG
            add_sheet_request = {
                'addSheet': {
                    'properties': {
                        'title': temp_name,
                        'gridProperties': {
                            'rowCount': 100,  # Giảm từ 1000 → 100
                            'columnCount': 35  # Tăng từ 20 → 35 để đủ cho tất cả cột cần thiết
                        }
                        # Bỏ tabColor để giảm complexity
                    }
                }
            }
            requests.append(add_sheet_request)

            # Thực hiện tạo sheet với timeout optimization
            response = await self.execute_with_retry(
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': requests}
                ),
                "Lỗi khi tạo sheet Historical Data",
                max_attempts=3,  # Giảm từ 5 → 3
                initial_delay=2   # Giảm từ 3 → 2
            )

            if response and 'replies' in response:
                historical_sheet_id = response['replies'][0]['addSheet']['properties']['sheetId']
                self.log(f"✅ Đã tạo sheet tạm thời với ID: {historical_sheet_id}")

                # ULTRA FAST MODE: Skip header creation để tránh timeout
                if not self._ultra_fast_mode:
                    await self.create_timeline_headers_optimized(historical_sheet_id)
                else:
                    self.log("⚡ ULTRA FAST MODE: Bỏ qua tạo headers để tránh timeout")

                # Rename về tên chuẩn sau khi tạo thành công
                rename_success = await self.execute_with_retry(
                    self.service.spreadsheets().batchUpdate(
                        spreadsheetId=self.spreadsheet_id,
                        body={'requests': [{
                            'updateSheetProperties': {
                                'properties': {
                                    'sheetId': historical_sheet_id,
                                    'title': 'Historical Data'
                                },
                                'fields': 'title'
                            }
                        }]}
                    ),
                    "Lỗi khi rename sheet về Historical Data",
                    max_attempts=3,
                    initial_delay=2
                )

                if rename_success:
                    self.log(f"✅ Đã rename sheet thành 'Historical Data' (ID: {historical_sheet_id})")
                else:
                    self.log(f"⚠️ Không thể rename, nhưng sheet vẫn hoạt động (ID: {historical_sheet_id})")

                # Refresh cache sau khi tạo
                await self.get_cached_sheet_metadata(force_refresh=True)

                return historical_sheet_id
            else:
                self.log("❌ Không thể lấy ID của sheet Historical Data vừa tạo")
                return None

        except Exception as e:
            self.log(f"❌ Lỗi khi tạo Historical Data sheet: {e}")
            return None

    async def create_timeline_headers_optimized(self, historical_sheet_id):
        """
        Tối ưu: Tạo tất cả headers cần thiết trong một lần
        """
        try:
            # Tạo headers động với tên cluster thật (sẽ được cập nhật sau)
            headers = [
                "Timestamp", "Thời gian", "Cluster 1", "Cluster 2", "Cluster 3",
                "Cluster 4", "Cluster 5", "Cluster 6", "",
                "% Cluster 1", "% Cluster 2", "% Cluster 3", "% Cluster 4", "% Cluster 5", "% Cluster 6",
                "Δ Cluster 1", "Δ Cluster 2", "Δ Cluster 3", "Δ Cluster 4", "Δ Cluster 5", "Δ Cluster 6",
                "📊 Cluster 1", "📊 Cluster 2", "📊 Cluster 3", "📊 Cluster 4", "📊 Cluster 5", "📊 Cluster 6"
            ]

            # Ghi header và pie chart headers trong một lần
            all_data = [
                headers,  # Dòng 1: Header chính
                [],       # Dòng 2: Trống để bắt đầu dữ liệu
                # ... (dòng 3-30 sẽ là dữ liệu)
            ]

            # Thêm 28 dòng trống cho dữ liệu
            for i in range(28):
                all_data.append([])

            # Dòng 31: Header cho Pie chart (sẽ được cập nhật với tên cluster thật)
            all_data.append(["Cluster 1", "Cluster 2", "Cluster 3", "Cluster 4", "Cluster 5", "Cluster 6"])
            # Dòng 32: Dữ liệu Pie chart (sẽ được cập nhật sau)
            all_data.append(["0%", "0%", "0%", "0%", "0%", "0%"])

            await self.execute_with_retry(
                self.service.spreadsheets().values().update(
                    spreadsheetId=self.spreadsheet_id,
                    range="'Historical Data'!A1:AA32",
                    valueInputOption="USER_ENTERED",
                    body={"values": all_data}
                ),
                "Lỗi khi tạo headers"
            )

            self.log("✅ Đã tạo headers tối ưu cho Historical Data")

        except Exception as e:
            self.log(f"❌ Lỗi khi tạo headers: {e}")

    async def update_historical_data_optimized(self, historical_sheet_id, clusters_data):
        """
        Tối ưu: Cập nhật dữ liệu và biểu đồ với ít API calls nhất
        Sử dụng tên cluster thật từ TOP GMV sheet
        """
        try:
            # 1. Lấy dữ liệu hiện tại và tìm dòng trống trong một lần
            response = await self.execute_with_retry(
                self.service.spreadsheets().values().get(
                    spreadsheetId=self.spreadsheet_id,
                    range="'Historical Data'!A2:AA29"
                ),
                "Lỗi khi lấy dữ liệu hiện tại"
            )

            # 2. Chuẩn bị dữ liệu mới
            current_time = datetime.now()
            timestamp = current_time.strftime("%Y-%m-%d %H:%M:%S")
            time_only = current_time.strftime("%H:%M")

            # Lấy dữ liệu từ TOP GMV sheet
            sheet = self.sheet_selector.currentText()
            gmv_response = await self.execute_with_retry(
                self.service.spreadsheets().values().get(
                    spreadsheetId=self.spreadsheet_id,
                    range=f"{sheet}!H3:I1000"
                ),
                "Lỗi khi lấy dữ liệu GMV"
            )

            # 3. Tính toán dữ liệu cluster với tên thật
            cluster_revenues, cluster_percentages, actual_clusters = self.calculate_cluster_data(gmv_response)

            # Log ngắn gọn hơn
            self.log(f"Tìm thấy {len(actual_clusters)} clusters: {', '.join(actual_clusters[:3])}{'...' if len(actual_clusters) > 3 else ''}")

            # 4. Tìm dòng trống và chuẩn bị dữ liệu
            next_row = self.find_next_empty_row(response)
            row_data, extended_clusters = self.prepare_row_data(timestamp, time_only, cluster_revenues, cluster_percentages, actual_clusters)

            # 4.5. Đảm bảo sheet có đủ cột trước khi batch update
            await self.expand_historical_sheet_if_needed(historical_sheet_id)

            # 5. Cập nhật tất cả trong một batch với tên cluster thật (sử dụng extended_clusters để đảm bảo đủ 6 cột)
            await self.batch_update_historical_data(historical_sheet_id, next_row, row_data, cluster_percentages, extended_clusters)

            # 6. Tính Delta cho Line chart (từ lần scrape thứ 2 trở đi)
            if next_row > 2:  # Có dữ liệu trước đó để so sánh
                self.log(f"Tính Delta cho dòng {next_row}...")
                # Sử dụng extended_clusters để đảm bảo consistency với dữ liệu đã ghi
                await self.calculate_and_add_trend_data(historical_sheet_id, next_row, extended_clusters, cluster_percentages)

            # 7. Tạo/cập nhật biểu đồ doanh thu timeline (từ lần scrape thứ 3 trở đi)
            if next_row >= 4:  # Có đủ dữ liệu để vẽ xu hướng
                await self.create_or_update_revenue_timeline_chart(historical_sheet_id)

            # 8. Tạo/cập nhật bảng tóm tắt doanh thu
            await self.create_or_update_revenue_summary_table(historical_sheet_id, extended_clusters, cluster_revenues, cluster_percentages)

            # 9. Validation cuối cùng (chỉ chạy khi không ở fast mode)
            if not self._fast_mode and next_row % 5 == 0:
                await self.validate_historical_data_integrity(historical_sheet_id)
            elif self._fast_mode:
                self.log("⚡ Fast mode: Bỏ qua validation để tăng tốc")

            self.log(f"✅ Đã cập nhật Historical Data tại dòng {next_row}")

        except Exception as e:
            self.log(f"❌ Lỗi khi cập nhật Historical Data tối ưu: {e}")

    def calculate_cluster_data(self, gmv_response):
        """Helper: Tính toán dữ liệu cluster từ response - Tối ưu để skip N/A và clusters không hợp lệ"""
        cluster_revenues = {}
        cluster_percentages = {}
        actual_clusters = []

        # Danh sách các cluster không hợp lệ cần bỏ qua
        invalid_clusters = {"N/A", "Unknown", "", "null", "undefined", "#N/A", "#VALUE!", "#REF!"}

        if gmv_response and 'values' in gmv_response:
            total_revenue = 0

            # Tính tổng doanh thu từng cluster
            for row in gmv_response['values']:
                if len(row) >= 2:
                    try:
                        revenue_str = str(row[0]).replace('₫', '').replace('.', '').replace(',', '').strip()
                        revenue = float(revenue_str) if revenue_str else 0
                        cluster = str(row[1]).strip() if len(row) > 1 else "Unknown"

                        # Bỏ qua các cluster không hợp lệ và chỉ tính cluster có doanh thu > 0
                        if cluster and cluster not in invalid_clusters and revenue > 0:
                            cluster_revenues[cluster] = cluster_revenues.get(cluster, 0) + revenue
                            total_revenue += revenue
                    except Exception as e:
                        # Log chi tiết hơn để debug
                        continue

            # Tính phần trăm và sắp xếp - chỉ lấy clusters có doanh thu thực tế
            if total_revenue > 0:
                sorted_clusters = sorted(cluster_revenues.items(), key=lambda x: x[1], reverse=True)
                for cluster, revenue in sorted_clusters[:6]:  # Top 6 clusters
                    if revenue > 0:  # Đảm bảo chỉ lấy cluster có doanh thu thực tế
                        percentage = (revenue / total_revenue) * 100
                        cluster_percentages[cluster] = percentage
                        actual_clusters.append(cluster)

            # Log chi tiết để debug
            if len(actual_clusters) < 6:
                self.log(f"⚠️ Chỉ tìm thấy {len(actual_clusters)} clusters hợp lệ: {actual_clusters}")
                self.log(f"📊 Sẽ sử dụng danh sách cluster cố định để đảm bảo biểu đồ hoạt động")
            else:
                self.log(f"✅ Tìm thấy đủ {len(actual_clusters)} clusters: {actual_clusters}")

        return cluster_revenues, cluster_percentages, actual_clusters

    def validate_chart_data(self, data_range, chart_type="Unknown"):
        """
        Helper: Kiểm tra và làm sạch dữ liệu trước khi tạo biểu đồ
        Đảm bảo không có giá trị N/A, null, hoặc empty gây lỗi biểu đồ
        """
        try:
            if not data_range or 'values' not in data_range:
                self.log(f"⚠️ {chart_type}: Không có dữ liệu để tạo biểu đồ")
                return False, []

            values = data_range['values']
            clean_data = []

            for row in values:
                clean_row = []
                for cell in row:
                    # Làm sạch dữ liệu: thay thế N/A, null, empty bằng 0
                    if cell is None or str(cell).strip() in ['', 'N/A', 'null', 'undefined', '#N/A', '#VALUE!', '#REF!']:
                        clean_row.append(0)
                    else:
                        try:
                            # Thử convert thành số nếu có thể
                            if isinstance(cell, str):
                                # Xử lý format tiền tệ
                                clean_value = cell.replace('₫', '').replace('.', '').replace(',', '.').strip()
                                if clean_value:
                                    clean_row.append(float(clean_value))
                                else:
                                    clean_row.append(0)
                            else:
                                clean_row.append(float(cell) if cell else 0)
                        except:
                            clean_row.append(0)

                clean_data.append(clean_row)

            # Kiểm tra xem có ít nhất một giá trị > 0 không
            has_valid_data = any(any(cell > 0 for cell in row if isinstance(cell, (int, float))) for row in clean_data)

            if not has_valid_data:
                self.log(f"⚠️ {chart_type}: Tất cả dữ liệu đều bằng 0, bỏ qua tạo biểu đồ")
                return False, clean_data

            self.log(f"✅ {chart_type}: Dữ liệu hợp lệ, có thể tạo biểu đồ")
            return True, clean_data

        except Exception as e:
            self.log(f"❌ Lỗi khi validate dữ liệu cho {chart_type}: {e}")
            return False, []

    async def get_cached_sheet_metadata(self, force_refresh=False):
        """
        Lấy sheet metadata với caching để tránh API calls không cần thiết
        """
        import time
        current_time = time.time()

        # Kiểm tra cache còn hiệu lực không
        if (not force_refresh and
            self._sheet_metadata_cache and
            (current_time - self._cache_timestamp) < self._cache_ttl):
            return self._sheet_metadata_cache

        # Refresh cache
        try:
            metadata = await self.execute_with_retry(
                self.service.spreadsheets().get(spreadsheetId=self.spreadsheet_id),
                "Lỗi khi lấy sheet metadata",
                max_attempts=3,
                initial_delay=2
            )

            if metadata:
                self._sheet_metadata_cache = metadata
                self._cache_timestamp = current_time
                self.log(f"🔄 Đã refresh sheet metadata cache")

            return metadata

        except Exception as e:
            self.log(f"❌ Lỗi khi lấy cached metadata: {e}")
            return self._sheet_metadata_cache  # Fallback to old cache

    async def force_cleanup_all_historical_sheets(self, historical_sheets):
        """
        FORCE CLEANUP: Xóa TẤT CẢ sheet Historical Data và tạo mới để tránh confusion
        """
        try:
            self.log(f"🚨 FORCE CLEANUP: Xóa tất cả {len(historical_sheets)} sheet Historical Data duplicate")

            # Xóa TẤT CẢ sheet duplicate
            delete_requests = []
            for sheet_info in historical_sheets:
                sheet_id = sheet_info['id']
                title = sheet_info['title']
                self.log(f"🗑️ Xóa: '{title}' (ID: {sheet_id})")
                delete_requests.append({
                    'deleteSheet': {
                        'sheetId': sheet_id
                    }
                })

            # Thực hiện xóa với timeout protection
            if delete_requests:
                delete_success = await self.execute_with_retry(
                    self.service.spreadsheets().batchUpdate(
                        spreadsheetId=self.spreadsheet_id,
                        body={'requests': delete_requests}
                    ),
                    "Lỗi khi force cleanup sheet duplicate",
                    max_attempts=3,
                    initial_delay=5
                )

                if delete_success:
                    self.log(f"✅ Đã xóa tất cả {len(historical_sheets)} sheet duplicate")
                    # Refresh cache
                    await self.get_cached_sheet_metadata(force_refresh=True)
                else:
                    self.log("⚠️ Không thể xóa hết, sẽ tạo sheet với tên unique")

            # Tạo sheet mới sạch sẽ
            self.log("📝 Tạo sheet Historical Data mới sau cleanup...")
            return await self.create_historical_sheet_optimized()

        except Exception as e:
            self.log(f"❌ Lỗi khi force cleanup: {e}")
            # Fallback: Tạo sheet với tên unique
            import time
            timestamp = int(time.time())
            return await self.create_historical_sheet_with_unique_name(f"Historical_Data_{timestamp}")

    async def create_historical_sheet_with_unique_name(self, sheet_name):
        """
        Tạo sheet với tên unique để tránh conflict
        """
        try:
            self.log(f"🆘 Tạo sheet với tên unique: {sheet_name}")

            add_sheet_request = {
                'addSheet': {
                    'properties': {
                        'title': sheet_name,
                        'gridProperties': {
                            'rowCount': 100,
                            'columnCount': 30
                        },
                        'tabColor': {
                            'red': 1.0,
                            'green': 0.5,
                            'blue': 0.0  # Orange để dễ nhận biết
                        }
                    }
                }
            }

            response = await self.execute_with_retry(
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': [add_sheet_request]}
                ),
                f"Lỗi khi tạo sheet {sheet_name}",
                max_attempts=3,
                initial_delay=3
            )

            if response and 'replies' in response:
                sheet_id = response['replies'][0]['addSheet']['properties']['sheetId']
                self.log(f"✅ Đã tạo sheet {sheet_name} (ID: {sheet_id})")

                # Tạo headers
                await self.create_timeline_headers_optimized(sheet_id)
                return sheet_id
            else:
                self.log(f"❌ Không thể tạo sheet {sheet_name}")
                return None

        except Exception as e:
            self.log(f"❌ Lỗi khi tạo sheet unique: {e}")
            return None

    async def final_check_no_duplicates(self):
        """
        Kiểm tra cuối cùng để đảm bảo không có duplicate sheets
        """
        try:
            if not self._fast_mode:  # Chỉ check khi không ở fast mode
                sheet_metadata = await self.get_cached_sheet_metadata(force_refresh=True)
                if sheet_metadata:
                    historical_count = 0
                    for sheet in sheet_metadata.get('sheets', []):
                        title = sheet['properties']['title']
                        if 'Historical' in title:
                            historical_count += 1

                    if historical_count > 1:
                        self.log(f"⚠️ CẢNH BÁO: Vẫn có {historical_count} sheet Historical sau cleanup!")
                    else:
                        self.log(f"✅ OK: Chỉ có {historical_count} sheet Historical")
        except:
            pass  # Không quan trọng nếu check này fail

    async def rename_sheet_to_historical_data(self, sheet_id):
        """
        Rename sheet về tên chuẩn 'Historical Data'
        """
        try:
            self.log(f"🔄 Đổi tên sheet ID {sheet_id} về 'Historical Data'")

            rename_success = await self.execute_with_retry(
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': [{
                        'updateSheetProperties': {
                            'properties': {
                                'sheetId': sheet_id,
                                'title': 'Historical Data'
                            },
                            'fields': 'title'
                        }
                    }]}
                ),
                "Lỗi khi rename sheet",
                max_attempts=3,
                initial_delay=2
            )

            if rename_success:
                self.log(f"✅ Đã rename sheet ID {sheet_id} thành 'Historical Data'")
                # Refresh cache
                await self.get_cached_sheet_metadata(force_refresh=True)
            else:
                self.log(f"⚠️ Không thể rename sheet ID {sheet_id}")

        except Exception as e:
            self.log(f"❌ Lỗi khi rename sheet: {e}")

    def validate_row_data_structure(self, row_data, extended_clusters):
        """
        Validation function để đảm bảo row_data có cấu trúc đúng
        """
        try:
            expected_length = 27  # 2 + 6 + 1 + 6 + 6 + 6 = timestamp + time + revenue + separator + percentage + delta + trend
            actual_length = len(row_data)

            if actual_length != expected_length:
                self.log(f"❌ VALIDATION ERROR: row_data length mismatch. Expected: {expected_length}, Actual: {actual_length}")
                return False

            # Kiểm tra cấu trúc
            checks = [
                (0, "timestamp", str),
                (1, "time", str),
                (8, "separator", str),  # Should be empty string
            ]

            for i in range(2, 8):  # Revenue columns (2-7)
                checks.append((i, f"revenue_{i-2}", str))

            for i in range(9, 15):  # Percentage columns (9-14)
                checks.append((i, f"percentage_{i-9}", (int, float)))

            for i in range(15, 21):  # Delta columns (15-20)
                checks.append((i, f"delta_{i-15}", (int, float)))

            for i in range(21, 27):  # Trend columns (21-26)
                checks.append((i, f"trend_{i-21}", str))

            for idx, name, expected_type in checks:
                if idx < len(row_data):
                    val = row_data[idx]
                    if not isinstance(val, expected_type):
                        self.log(f"❌ VALIDATION ERROR: {name} at index {idx} should be {expected_type}, got {type(val)}: {val}")
                        return False
                else:
                    self.log(f"❌ VALIDATION ERROR: Missing {name} at index {idx}")
                    return False

            # Kiểm tra extended_clusters
            if len(extended_clusters) != 6:
                self.log(f"❌ VALIDATION ERROR: extended_clusters should have 6 elements, got {len(extended_clusters)}")
                return False

            self.log(f"✅ VALIDATION PASSED: row_data structure is correct ({actual_length} elements)")
            return True

        except Exception as e:
            self.log(f"❌ VALIDATION ERROR: Exception during validation: {e}")
            return False

    async def get_existing_charts(self, sheet_id):
        """
        Helper: Lấy danh sách các biểu đồ hiện có trong sheet
        """
        try:
            spreadsheet = await self.execute_with_retry(
                self.service.spreadsheets().get(spreadsheetId=self.spreadsheet_id),
                "Lỗi khi lấy thông tin spreadsheet"
            )

            if not spreadsheet or 'sheets' not in spreadsheet:
                return []

            # Tìm sheet có ID tương ứng
            target_sheet = None
            for sheet in spreadsheet['sheets']:
                if sheet['properties']['sheetId'] == sheet_id:
                    target_sheet = sheet
                    break

            if not target_sheet or 'charts' not in target_sheet:
                return []

            return target_sheet.get('charts', [])

        except Exception as e:
            self.log(f"❌ Lỗi khi lấy danh sách biểu đồ: {e}")
            return []

    def find_next_empty_row(self, response):
        """Helper: Tìm dòng trống tiếp theo"""
        next_row = 2  # Bắt đầu từ dòng 2
        if response and 'values' in response:
            for i, row in enumerate(response['values']):
                if not row or not row[0].strip():
                    next_row = i + 2
                    break
            else:
                next_row = len(response['values']) + 2
        return next_row

    def prepare_row_data(self, timestamp, time_only, cluster_revenues, cluster_percentages, actual_clusters):
        """Helper: Chuẩn bị dữ liệu dòng mới với tên cluster thật (đảm bảo đủ 6 cột) - Tối ưu cho biểu đồ"""
        row_data = [timestamp, time_only]

        # Sử dụng danh sách cluster cố định để đảm bảo tính nhất quán cho biểu đồ
        # Điều này giúp biểu đồ không bị lỗi khi có cluster N/A hoặc thiếu dữ liệu
        fixed_clusters = CLUSTERS.copy()  # ["ELHA", "HB", "FMCG", "MKB", "Lifestyle", "Fashion"]

        # Nếu có ít hơn 6 clusters thực tế, vẫn sử dụng danh sách cố định
        # Nhưng ưu tiên các cluster thực tế có doanh thu cao nhất
        extended_clusters = []

        # Thêm các cluster thực tế có doanh thu vào đầu danh sách
        for cluster in actual_clusters[:6]:
            if cluster in fixed_clusters:
                extended_clusters.append(cluster)

        # Thêm các cluster cố định còn lại để đủ 6 cột
        for cluster in fixed_clusters:
            if cluster not in extended_clusters and len(extended_clusters) < 6:
                extended_clusters.append(cluster)

        # Đảm bảo luôn có đủ 6 cột (fallback)
        while len(extended_clusters) < 6:
            extended_clusters.append(f"Cluster {len(extended_clusters) + 1}")

        # Thêm doanh thu từng cluster (6 cột) - sử dụng extended_clusters
        for i in range(6):
            cluster = extended_clusters[i]
            if cluster in cluster_revenues and cluster_revenues[cluster] > 0:
                revenue = cluster_revenues[cluster]
                row_data.append(f"{revenue:,.0f}".replace(",", "."))
            else:
                row_data.append("0")

        row_data.append("")  # Separator

        # Thêm phần trăm từng cluster (6 cột) - dạng số để Google Sheets format %
        for i in range(6):
            cluster = extended_clusters[i]
            if cluster in cluster_percentages and cluster_percentages[cluster] > 0:
                percentage = cluster_percentages[cluster]
                row_data.append(percentage / 100)  # Chuyển về decimal
            else:
                row_data.append(0.0)

        # Thêm placeholder cho Delta columns (6 cột) - sẽ được cập nhật sau bởi calculate_and_add_trend_data
        for i in range(6):
            row_data.append(0.0)  # Delta placeholder

        # Thêm placeholder cho Trend indicator columns (6 cột) - sẽ được cập nhật sau
        for i in range(6):
            row_data.append("➡️")  # Trend placeholder

        # Validation: Kiểm tra cấu trúc row_data
        if not self.validate_row_data_structure(row_data, extended_clusters):
            self.log("❌ CRITICAL ERROR: row_data structure validation failed!")
            # Tạo row_data backup với cấu trúc đúng
            row_data = [timestamp, time_only] + ["0"] * 6 + [""] + [0.0] * 6 + [0.0] * 6 + ["➡️"] * 6

        return row_data, extended_clusters  # Trả về cả extended_clusters để sử dụng cho header

    async def batch_update_historical_data(self, historical_sheet_id, next_row, row_data, cluster_percentages, actual_clusters):
        """Helper: Cập nhật tất cả dữ liệu trong một batch - Tối ưu timeout"""
        try:
            requests = []
            self.log(f"🔄 Batch update dòng {next_row} với {len(row_data)} cột dữ liệu...")

            # 1. Cập nhật header với tên cluster thật (chỉ lần đầu tiên)
            if next_row == 2:  # Lần đầu tiên
                header_data = ["Timestamp", "Thời gian"]

                # Thêm tên cluster thật cho revenue columns
                for i in range(6):
                    if i < len(actual_clusters):
                        header_data.append(actual_clusters[i])
                    else:
                        header_data.append(f"Cluster {i+1}")

                header_data.append("")  # Separator

                # Thêm tên cluster thật cho percentage columns
                for i in range(6):
                    if i < len(actual_clusters):
                        header_data.append(f"% {actual_clusters[i]}")
                    else:
                        header_data.append(f"% Cluster {i+1}")

                # Thêm tên cluster thật cho Delta columns
                for i in range(6):
                    if i < len(actual_clusters):
                        header_data.append(f"Δ {actual_clusters[i]}")
                    else:
                        header_data.append(f"Δ Cluster {i+1}")

                # Thêm tên cluster thật cho trend columns
                for i in range(6):
                    if i < len(actual_clusters):
                        header_data.append(f"📊 {actual_clusters[i]}")
                    else:
                        header_data.append(f"📊 Cluster {i+1}")

                requests.append({
                    'updateCells': {
                        'range': {
                            'sheetId': historical_sheet_id,
                            'startRowIndex': 0,  # Dòng 1
                            'endRowIndex': 1,
                            'startColumnIndex': 0,
                            'endColumnIndex': len(header_data)
                        },
                        'rows': [{
                            'values': [{'userEnteredValue': {'stringValue': str(val)}} for val in header_data]
                        }],
                        'fields': 'userEnteredValue'
                    }
                })

                # Cập nhật header Pie chart với tên cluster thật
                pie_header = []
                for i in range(6):
                    if i < len(actual_clusters):
                        pie_header.append(actual_clusters[i])
                    else:
                        pie_header.append(f"Cluster {i+1}")

                requests.append({
                    'updateCells': {
                        'range': {
                            'sheetId': historical_sheet_id,
                            'startRowIndex': 30,  # Dòng 31
                            'endRowIndex': 31,
                            'startColumnIndex': 0,
                            'endColumnIndex': 6
                        },
                        'rows': [{
                            'values': [{'userEnteredValue': {'stringValue': str(val)}} for val in pie_header]
                        }],
                        'fields': 'userEnteredValue'
                    }
                })

            # 2. Ghi dữ liệu chính với mixed data types
            # row_data structure: [timestamp, time_only, revenue1-6, separator, percentage1-6, delta1-6, trend1-6]
            # Indices: 0=timestamp, 1=time_only, 2-7=revenue, 8=separator, 9-14=percentage, 15-20=delta, 21-26=trend
            cell_values = []
            for i, val in enumerate(row_data):
                if i >= 9 and i <= 14:  # Cột J-O (percentage columns: indices 9-14)
                    # Đảm bảo percentage được lưu dưới dạng số
                    if isinstance(val, (int, float)):
                        cell_values.append({'userEnteredValue': {'numberValue': float(val)}})
                    else:
                        # Fallback nếu val không phải số
                        try:
                            cell_values.append({'userEnteredValue': {'numberValue': float(val)}})
                        except:
                            cell_values.append({'userEnteredValue': {'numberValue': 0.0}})
                elif i >= 15 and i <= 20:  # Cột P-U (delta columns: indices 15-20)
                    # Delta cũng là số
                    if isinstance(val, (int, float)):
                        cell_values.append({'userEnteredValue': {'numberValue': float(val)}})
                    else:
                        try:
                            cell_values.append({'userEnteredValue': {'numberValue': float(val)}})
                        except:
                            cell_values.append({'userEnteredValue': {'numberValue': 0.0}})
                else:
                    # Timestamp, time, revenue, separator, trend indicators
                    cell_values.append({'userEnteredValue': {'stringValue': str(val)}})

            # Đảm bảo không vượt quá số cột có sẵn (tối đa 35 cột)
            max_columns = min(len(row_data), 35)

            requests.append({
                'updateCells': {
                    'range': {
                        'sheetId': historical_sheet_id,
                        'startRowIndex': next_row - 1,
                        'endRowIndex': next_row,
                        'startColumnIndex': 0,
                        'endColumnIndex': max_columns  # Giới hạn số cột để tránh lỗi
                    },
                    'rows': [{
                        'values': cell_values[:max_columns]  # Chỉ lấy số cell values tương ứng
                    }],
                    'fields': 'userEnteredValue'
                }
            })

            # 2.1. Áp dụng format % cho cột percentage (J-O)
            requests.append({
                'repeatCell': {
                    'range': {
                        'sheetId': historical_sheet_id,
                        'startRowIndex': next_row - 1,
                        'endRowIndex': next_row,
                        'startColumnIndex': 9,  # Cột J
                        'endColumnIndex': 15    # Cột O
                    },
                    'cell': {
                        'userEnteredFormat': {
                            'numberFormat': {
                                'type': 'PERCENT',
                                'pattern': '0.0%'
                            }
                        }
                    },
                    'fields': 'userEnteredFormat.numberFormat'
                }
            })

            # 3. Cập nhật dữ liệu Pie chart (dòng 32) với số và format %
            pie_data = []
            for i in range(6):
                cluster = actual_clusters[i]  # actual_clusters đã được extend thành 6 phần tử
                if cluster in cluster_percentages:
                    percentage = cluster_percentages[cluster]
                    pie_data.append(percentage / 100)  # Chuyển về decimal cho Google Sheets
                else:
                    pie_data.append(0.0)

            requests.append({
                'updateCells': {
                    'range': {
                        'sheetId': historical_sheet_id,
                        'startRowIndex': 31,  # Dòng 32
                        'endRowIndex': 32,
                        'startColumnIndex': 0,
                        'endColumnIndex': 6
                    },
                    'rows': [{
                        'values': [{'userEnteredValue': {'numberValue': val}} for val in pie_data]
                    }],
                    'fields': 'userEnteredValue,userEnteredFormat'
                }
            })

            # 4. Áp dụng format % cho dòng 32
            requests.append({
                'repeatCell': {
                    'range': {
                        'sheetId': historical_sheet_id,
                        'startRowIndex': 31,  # Dòng 32
                        'endRowIndex': 32,
                        'startColumnIndex': 0,
                        'endColumnIndex': 6
                    },
                    'cell': {
                        'userEnteredFormat': {
                            'numberFormat': {
                                'type': 'PERCENT',
                                'pattern': '0.0%'
                            }
                        }
                    },
                    'fields': 'userEnteredFormat.numberFormat'
                }
            })

            # 3. Tính và ghi Delta nếu không phải dòng đầu tiên
            if next_row > 2:
                delta_data = await self.calculate_delta_optimized(historical_sheet_id, next_row, cluster_percentages, actual_clusters)
                if delta_data:
                    requests.append({
                        'updateCells': {
                            'range': {
                                'sheetId': historical_sheet_id,
                                'startRowIndex': next_row - 1,
                                'endRowIndex': next_row,
                                'startColumnIndex': 15,  # Cột P
                                'endColumnIndex': 27     # Cột AA
                            },
                            'rows': [{
                                'values': [{'userEnteredValue': {'stringValue': str(val)}} for val in delta_data]
                            }],
                            'fields': 'userEnteredValue'
                        }
                    })

            # 4. Tạo biểu đồ nếu cần
            chart_requests = await self.get_chart_requests_if_needed(historical_sheet_id, next_row)
            requests.extend(chart_requests)

            # Thực hiện tất cả cập nhật trong một batch với timeout cao
            if requests:
                self.log(f"🚀 Thực hiện batch update với {len(requests)} requests...")
                await self.execute_with_retry(
                    self.service.spreadsheets().batchUpdate(
                        spreadsheetId=self.spreadsheet_id,
                        body={'requests': requests}
                    ),
                    "Lỗi khi batch update Historical Data",
                    max_attempts=3,  # Giảm attempts cho batch lớn
                    initial_delay=5  # Tăng delay cho batch operations
                )
                self.log(f"✅ Hoàn thành batch update {len(requests)} requests")

        except Exception as e:
            self.log(f"❌ Lỗi khi batch update: {e}")

    async def calculate_delta_optimized(self, historical_sheet_id, current_row, cluster_percentages, actual_clusters):
        """Helper: Tính Delta tối ưu"""
        try:
            if current_row <= 2:
                return None

            # Lấy dữ liệu dòng trước
            previous_row = current_row - 1
            response = await self.execute_with_retry(
                self.service.spreadsheets().values().get(
                    spreadsheetId=self.spreadsheet_id,
                    range=f"'Historical Data'!J{previous_row}:O{previous_row}"
                ),
                "Lỗi khi lấy dữ liệu dòng trước"
            )

            if not response or 'values' not in response or not response['values']:
                return None

            previous_data = response['values'][0]
            delta_data = []

            # Tính Delta cho 6 clusters
            for i in range(6):
                if i < len(actual_clusters):
                    cluster = actual_clusters[i]
                    current_percentage = cluster_percentages.get(cluster, 0)

                    if i < len(previous_data):
                        try:
                            prev_str = str(previous_data[i]).replace('%', '').strip()
                            previous_percentage = float(prev_str) if prev_str else 0
                        except:
                            previous_percentage = 0
                    else:
                        previous_percentage = 0

                    delta = current_percentage - previous_percentage
                    delta_data.append(f"{delta:.1f}")
                else:
                    delta_data.append("0.0")

            # Thêm trend indicators
            for i in range(6):
                if i < len(delta_data):
                    try:
                        delta_val = float(delta_data[i])
                        if delta_val > 0.5:
                            delta_data.append("🔥")
                        elif delta_val > 0:
                            delta_data.append("📈")
                        elif delta_val < -0.5:
                            delta_data.append("❄️")
                        elif delta_val < 0:
                            delta_data.append("📉")
                        else:
                            delta_data.append("➡️")
                    except:
                        delta_data.append("➡️")
                else:
                    delta_data.append("➡️")

            return delta_data

        except Exception as e:
            print(f"[DEBUG] Lỗi khi tính Delta: {e}")
            return None

    async def get_chart_requests_if_needed(self, historical_sheet_id, current_row):
        """Helper: Tạo chart requests nếu cần"""
        try:
            # Kiểm tra xem đã có biểu đồ chưa
            existing_charts = await self.check_existing_charts(historical_sheet_id)

            requests = []
            scrape_count = current_row - 1

            if not existing_charts and scrape_count == 1:
                # Tạo Pie chart lần đầu
                pie_chart_request = self.create_pie_chart_request(historical_sheet_id)
                requests.append(pie_chart_request)
                print("[DEBUG] Tạo Pie chart lần đầu")

            elif len(existing_charts) == 1 and scrape_count >= 2:
                # Thêm Line chart khi có đủ dữ liệu Delta (từ lần scrape thứ 2)
                line_chart_request = self.create_line_chart_request(historical_sheet_id)
                requests.append(line_chart_request)
                print("[DEBUG] Thêm Line chart với Delta data")

            return requests

        except Exception as e:
            print(f"[DEBUG] Lỗi khi tạo chart requests: {e}")
            return []

    def create_pie_chart_request(self, historical_sheet_id):
        """Helper: Tạo Pie chart request"""
        return {
            'addChart': {
                'chart': {
                    'spec': {
                        'title': 'Phân bố % Cluster (Dữ liệu mới nhất)',
                        'pieChart': {
                            'legendPosition': 'RIGHT_LEGEND',
                            'domain': {
                                'sourceRange': {
                                    'sources': [{
                                        'sheetId': historical_sheet_id,
                                        'startRowIndex': 30,
                                        'endRowIndex': 31,
                                        'startColumnIndex': 0,
                                        'endColumnIndex': 6
                                    }]
                                }
                            },
                            'series': {
                                'sourceRange': {
                                    'sources': [{
                                        'sheetId': historical_sheet_id,
                                        'startRowIndex': 31,
                                        'endRowIndex': 32,
                                        'startColumnIndex': 0,
                                        'endColumnIndex': 6
                                    }]
                                }
                            }
                        }
                    },
                    'position': {
                        'overlayPosition': {
                            'anchorCell': {
                                'sheetId': historical_sheet_id,
                                'rowIndex': 35,
                                'columnIndex': 16
                            },
                            'widthPixels': 500,
                            'heightPixels': 400
                        }
                    }
                }
            }
        }

    def create_line_chart_request(self, historical_sheet_id):
        """Helper: Tạo Line chart request"""
        return {
            'addChart': {
                'chart': {
                    'spec': {
                        'title': 'Xu hướng thay đổi GMV theo ngành hàng',
                        'basicChart': {
                            'chartType': 'LINE',
                            'legendPosition': 'RIGHT_LEGEND',
                            'axis': [
                                {
                                    'position': 'BOTTOM_AXIS',
                                    'title': 'Thời gian'
                                },
                                {
                                    'position': 'LEFT_AXIS',
                                    'title': 'Sự thay đổi GMV (Delta)'
                                }
                            ],
                            'domains': [{
                                'domain': {
                                    'sourceRange': {
                                        'sources': [{
                                            'sheetId': historical_sheet_id,
                                            'startRowIndex': 2,
                                            'endRowIndex': 29,
                                            'startColumnIndex': 1,
                                            'endColumnIndex': 2
                                        }]
                                    }
                                }
                            }],
                            'series': [
                                {
                                    'series': {
                                        'sourceRange': {
                                            'sources': [{
                                                'sheetId': historical_sheet_id,
                                                'startRowIndex': 2,
                                                'endRowIndex': 29,
                                                'startColumnIndex': 15,
                                                'endColumnIndex': 16
                                            }]
                                        }
                                    },
                                    'targetAxis': 'LEFT_AXIS'
                                },
                                {
                                    'series': {
                                        'sourceRange': {
                                            'sources': [{
                                                'sheetId': historical_sheet_id,
                                                'startRowIndex': 2,
                                                'endRowIndex': 29,
                                                'startColumnIndex': 16,
                                                'endColumnIndex': 17
                                            }]
                                        }
                                    },
                                    'targetAxis': 'LEFT_AXIS'
                                },
                                {
                                    'series': {
                                        'sourceRange': {
                                            'sources': [{
                                                'sheetId': historical_sheet_id,
                                                'startRowIndex': 2,
                                                'endRowIndex': 29,
                                                'startColumnIndex': 17,
                                                'endColumnIndex': 18
                                            }]
                                        }
                                    },
                                    'targetAxis': 'LEFT_AXIS'
                                },
                                {
                                    'series': {
                                        'sourceRange': {
                                            'sources': [{
                                                'sheetId': historical_sheet_id,
                                                'startRowIndex': 2,
                                                'endRowIndex': 29,
                                                'startColumnIndex': 18,
                                                'endColumnIndex': 19
                                            }]
                                        }
                                    },
                                    'targetAxis': 'LEFT_AXIS'
                                },
                                {
                                    'series': {
                                        'sourceRange': {
                                            'sources': [{
                                                'sheetId': historical_sheet_id,
                                                'startRowIndex': 2,
                                                'endRowIndex': 29,
                                                'startColumnIndex': 19,
                                                'endColumnIndex': 20
                                            }]
                                        }
                                    },
                                    'targetAxis': 'LEFT_AXIS'
                                },
                                {
                                    'series': {
                                        'sourceRange': {
                                            'sources': [{
                                                'sheetId': historical_sheet_id,
                                                'startRowIndex': 2,
                                                'endRowIndex': 29,
                                                'startColumnIndex': 20,
                                                'endColumnIndex': 21
                                            }]
                                        }
                                    },
                                    'targetAxis': 'LEFT_AXIS'
                                }
                            ]
                        }
                    },
                    'position': {
                        'overlayPosition': {
                            'anchorCell': {
                                'sheetId': historical_sheet_id,
                                'rowIndex': 55,
                                'columnIndex': 30
                            },
                            'widthPixels': 800,
                            'heightPixels': 400
                        }
                    }
                }
            }
        }

    async def expand_historical_sheet_if_needed(self, historical_sheet_id):
        """
        Mở rộng sheet Historical Data nếu cần thiết để chứa biểu đồ
        """
        try:
            self.log("Kiểm tra và mở rộng sheet Historical Data...")

            # Lấy thông tin sheet từ cache
            sheet_metadata = await self.get_cached_sheet_metadata()

            current_columns = 15  # Mặc định
            for sheet in sheet_metadata.get('sheets', []):
                if sheet['properties']['sheetId'] == historical_sheet_id:
                    current_columns = sheet['properties']['gridProperties'].get('columnCount', 15)
                    break

            # Nếu cần mở rộng
            if current_columns < 35:
                self.log(f"Mở rộng sheet từ {current_columns} cột thành 35 cột...")

                expand_request = {
                    'updateSheetProperties': {
                        'properties': {
                            'sheetId': historical_sheet_id,
                            'gridProperties': {
                                'columnCount': 35
                            }
                        },
                        'fields': 'gridProperties.columnCount'
                    }
                }

                await self.execute_with_retry(
                    self.service.spreadsheets().batchUpdate(
                        spreadsheetId=self.spreadsheet_id,
                        body={'requests': [expand_request]}
                    ),
                    "Lỗi khi mở rộng sheet"
                )

                self.log("✅ Đã mở rộng sheet thành công")
            else:
                self.log("Sheet đã đủ rộng, không cần mở rộng")

        except Exception as e:
            self.log(f"❌ Lỗi khi mở rộng sheet: {e}")

    async def create_timeline_headers(self, historical_sheet_id):
        """
        Tạo header cơ bản cho timeline tracking trong Historical Data
        Header sẽ được cập nhật động khi có dữ liệu thực tế
        """
        try:
            self.log("Tạo header timeline cơ bản cho Historical Data...")

            # Tạo header cơ bản - sẽ được cập nhật động sau
            headers = [
                "Timestamp",  # A
                "Thời gian",  # B
                "Cluster 1",  # C
                "Cluster 2",  # D
                "Cluster 3",  # E
                "Cluster 4",  # F
                "Cluster 5",  # G
                "Cluster 6",  # H
                "",           # I - Separator
                "% Cluster 1", # J
                "% Cluster 2", # K
                "% Cluster 3", # L
                "% Cluster 4", # M
                "% Cluster 5", # N
                "% Cluster 6"  # O
            ]

            header_request = {
                'updateCells': {
                    'range': {
                        'sheetId': historical_sheet_id,
                        'startRowIndex': 0,
                        'endRowIndex': 1,
                        'startColumnIndex': 0,
                        'endColumnIndex': len(headers)
                    },
                    'rows': [
                        {
                            'values': [{'userEnteredValue': {'stringValue': header}} for header in headers]
                        }
                    ],
                    'fields': 'userEnteredValue'
                }
            }

            # Định dạng header
            format_request = {
                'repeatCell': {
                    'range': {
                        'sheetId': historical_sheet_id,
                        'startRowIndex': 0,
                        'endRowIndex': 1,
                        'startColumnIndex': 0,
                        'endColumnIndex': len(headers)
                    },
                    'cell': {
                        'userEnteredFormat': {
                            'backgroundColor': {'red': 0.2, 'green': 0.4, 'blue': 0.8},
                            'horizontalAlignment': 'CENTER',
                            'textFormat': {'bold': True, 'foregroundColor': {'red': 1, 'green': 1, 'blue': 1}}
                        }
                    },
                    'fields': 'userEnteredFormat(backgroundColor,textFormat,horizontalAlignment)'
                }
            }

            await self.execute_with_retry(
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': [header_request, format_request]}
                ),
                "Lỗi khi tạo timeline headers"
            )

            self.log("✅ Đã tạo timeline headers cơ bản (sẽ cập nhật động)")

        except Exception as e:
            self.log(f"❌ Lỗi khi tạo timeline headers: {e}")

    # DEPRECATED: Hàm cũ đã được thay thế bằng update_historical_data_optimized()
    # async def add_timestamp_data(self, historical_sheet_id, clusters_data):
    #     """
    #     Thêm dữ liệu timestamp mới vào timeline - lấy từ sheet TOP GMV
    #     """
        try:
            current_time = datetime.now()
            timestamp = current_time.strftime("%Y-%m-%d %H:%M:%S")
            time_only = current_time.strftime("%H:%M")

            self.log(f"Thêm dữ liệu timestamp: {timestamp}")

            # Lấy dữ liệu thực tế từ sheet TOP GMV
            sheet = self.sheet_selector.currentText()

            # Lấy dữ liệu từ cột H (Revenue) và I (Cluster)
            response = await self.execute_with_retry(
                self.service.spreadsheets().values().get(
                    spreadsheetId=self.spreadsheet_id,
                    range=f"{sheet}!H3:I1000"  # Revenue và Cluster
                ),
                "Lỗi khi lấy dữ liệu Revenue và Cluster"
            )

            if not response or 'values' not in response:
                self.log("❌ Không thể lấy dữ liệu từ sheet TOP GMV")
                return

            # Tính toán doanh thu từng cluster từ dữ liệu thực tế
            cluster_revenues = {}
            total_revenue = 0

            for row in response['values']:
                if len(row) >= 2:
                    revenue_str = str(row[0]).strip()  # Cột H - Revenue
                    cluster_name = str(row[1]).strip() if len(row) > 1 else "Khác"  # Cột I - Cluster

                    # Xử lý revenue string - chú ý định dạng thập phân
                    revenue_clean = revenue_str.replace("₫", "").strip()
                    try:
                        if revenue_clean and revenue_clean != "":
                            # Xử lý định dạng số: 10,000,000.29 hoặc 10.000.000,29
                            if "," in revenue_clean and "." in revenue_clean:
                                # Kiểm tra xem dấu nào là thập phân
                                last_comma = revenue_clean.rfind(",")
                                last_dot = revenue_clean.rfind(".")

                                if last_comma > last_dot:
                                    # Dấu phẩy là thập phân: 10.000.000,29
                                    revenue_clean = revenue_clean.replace(".", "").replace(",", ".")
                                else:
                                    # Dấu chấm là thập phân: 10,000,000.29
                                    revenue_clean = revenue_clean.replace(",", "")
                            elif "," in revenue_clean:
                                # Chỉ có dấu phẩy - có thể là phân cách nghìn hoặc thập phân
                                parts = revenue_clean.split(",")
                                if len(parts) == 2 and len(parts[1]) <= 2:
                                    # Có thể là thập phân: 1000,50
                                    revenue_clean = revenue_clean.replace(",", ".")
                                else:
                                    # Là phân cách nghìn: 1,000,000
                                    revenue_clean = revenue_clean.replace(",", "")
                            elif "." in revenue_clean:
                                # Chỉ có dấu chấm - có thể là phân cách nghìn hoặc thập phân
                                parts = revenue_clean.split(".")
                                if len(parts) == 2 and len(parts[1]) <= 2:
                                    # Có thể là thập phân: 1000.50
                                    pass  # Giữ nguyên
                                else:
                                    # Là phân cách nghìn: 1.000.000
                                    revenue_clean = revenue_clean.replace(".", "")

                            revenue = float(revenue_clean)
                        else:
                            revenue = 0
                    except Exception as e:
                        self.log(f"Lỗi parse revenue '{revenue_str}': {e}")
                        revenue = 0

                    if revenue > 0 and cluster_name:
                        if cluster_name not in cluster_revenues:
                            cluster_revenues[cluster_name] = 0
                        cluster_revenues[cluster_name] += revenue
                        total_revenue += revenue

            self.log(f"Tổng doanh thu: {total_revenue:,.0f}")
            self.log(f"Clusters tìm thấy: {list(cluster_revenues.keys())}")

            # Tính phần trăm
            cluster_percentages = {}
            for cluster, revenue in cluster_revenues.items():
                percentage = (revenue / total_revenue * 100) if total_revenue > 0 else 0
                cluster_percentages[cluster] = percentage
                self.log(f"{cluster}: {revenue:,.0f} ({percentage:.1f}%)")

            # Lấy danh sách cluster thực tế từ dữ liệu (sắp xếp theo doanh thu giảm dần)
            actual_clusters = sorted(cluster_revenues.keys(), key=lambda x: cluster_revenues[x], reverse=True)
            self.log(f"Clusters theo thứ tự doanh thu: {actual_clusters}")

            # Cập nhật header nếu cần thiết để phù hợp với clusters thực tế
            await self.update_timeline_headers_if_needed(historical_sheet_id, actual_clusters)

            row_data = [timestamp, time_only]

            # Thêm doanh thu từng cluster theo thứ tự thực tế
            for cluster in actual_clusters:
                revenue = cluster_revenues.get(cluster, 0)
                row_data.append(revenue)

            # Đảm bảo có đủ 6 cột cho doanh thu (thêm 0 nếu thiếu)
            while len(row_data) < 8:  # 2 cột đầu + 6 cột cluster
                row_data.append(0)

            row_data.append("")  # Separator

            # Thêm phần trăm từng cluster theo thứ tự thực tế
            for cluster in actual_clusters:
                percentage = cluster_percentages.get(cluster, 0)
                row_data.append(f"{percentage:.1f}%")

            # Đảm bảo có đủ 6 cột cho phần trăm (thêm 0% nếu thiếu)
            while len(row_data) < 15:  # 2 + 6 + 1 + 6
                row_data.append("0.0%")

            # Tìm dòng trống tiếp theo - chỉ tính dữ liệu thực sự (không tính header/title ở dòng 30+)
            response = await self.execute_with_retry(
                self.service.spreadsheets().values().get(
                    spreadsheetId=self.spreadsheet_id,
                    range="'Historical Data'!A2:A29"  # Chỉ kiểm tra vùng dữ liệu thực sự
                ),
                "Lỗi khi tìm dòng trống"
            )

            next_row = 2  # Bắt đầu từ dòng 2 (sau header)
            if response and 'values' in response:
                # Tìm dòng trống đầu tiên trong vùng dữ liệu
                data_rows = response['values']
                next_row = 2  # Mặc định bắt đầu từ dòng 2
                for i, row in enumerate(data_rows):
                    if not row or not row[0].strip():  # Dòng trống hoặc không có timestamp
                        next_row = i + 2  # +2 vì bắt đầu từ A2
                        break
                else:
                    # Nếu không có dòng trống, thêm vào cuối vùng dữ liệu
                    next_row = len(data_rows) + 2

            # Debug: Kiểm tra số cột trong row_data (chỉ hiển thị trong Terminal)
            print(f"[DEBUG] row_data có {len(row_data)} cột")

            # Tính toán range động dựa trên số cột thực tế
            end_column_index = len(row_data) - 1
            if end_column_index < 14:  # Tối thiểu đến cột O (index 14)
                end_column_index = 14

            # Chuyển đổi index thành ký tự cột
            def column_index_to_letter(index):
                result = ""
                while index >= 0:
                    result = chr(index % 26 + ord('A')) + result
                    index = index // 26 - 1
                return result

            end_column = column_index_to_letter(end_column_index)
            range_str = f"'Historical Data'!A{next_row}:{end_column}{next_row}"

            print(f"[DEBUG] Sử dụng range {range_str} cho {len(row_data)} cột")

            # Thêm dữ liệu vào dòng mới
            await self.execute_with_retry(
                self.service.spreadsheets().values().update(
                    spreadsheetId=self.spreadsheet_id,
                    range=range_str,
                    valueInputOption="USER_ENTERED",
                    body={"values": [row_data]}
                ),
                "Lỗi khi thêm timestamp data"
            )

            # Thông báo về trạng thái biểu đồ dựa trên số lần scrape
            scrape_count = next_row - 1

            # Cảnh báo nếu dữ liệu có thể xung đột với layout
            if scrape_count > 20:
                self.log(f"⚠️ Cảnh báo: Đã có {scrape_count} lần scrape. Biểu đồ đã được tự động di chuyển để tránh xung đột.")
                if scrape_count > 50:
                    self.log("💡 Gợi ý: Nên tạo sheet mới cho phiên scrape tiếp theo để tối ưu hiệu suất.")
            if scrape_count == 1:  # Lần scrape đầu tiên
                self.log("📊 Lần scrape đầu tiên: Đã có dữ liệu baseline. Pie chart đã sẵn sàng!")
                self.log("⏳ Cần thêm 2 lần scrape nữa để line chart hiển thị xu hướng đầy đủ")
            elif scrape_count == 2:  # Lần scrape thứ 2
                await self.calculate_and_add_trend_data(historical_sheet_id, next_row, actual_clusters, cluster_percentages)
                self.log("📊 Lần scrape thứ 2: Đã có Delta đầu tiên! Line chart bắt đầu có dữ liệu")
                self.log("⏳ Cần thêm 1 lần scrape nữa để thấy xu hướng tăng/giảm rõ ràng")
            elif scrape_count == 3:  # Lần scrape thứ 3
                await self.calculate_and_add_trend_data(historical_sheet_id, next_row, actual_clusters, cluster_percentages)
                self.log("📈 Lần scrape thứ 3: Line chart đã có thể hiển thị xu hướng tăng/giảm!")
                self.log("✅ Từ giờ mỗi lần scrape sẽ cập nhật xu hướng real-time")
            else:  # Lần scrape thứ 4 trở đi
                await self.calculate_and_add_trend_data(historical_sheet_id, next_row, actual_clusters, cluster_percentages)
                self.log(f"📊 Lần scrape thứ {scrape_count}: Line chart đang cập nhật xu hướng real-time")

            self.log(f"✅ Đã thêm dữ liệu timestamp {timestamp} vào dòng {next_row}")

        except Exception as e:
            self.log(f"❌ Lỗi khi thêm timestamp data: {e}")
            import traceback
            self.log(traceback.format_exc())

    async def calculate_and_add_trend_data(self, historical_sheet_id, current_row, actual_clusters, current_percentages):
        """
        Tính toán tốc độ tăng trưởng và xu hướng thực sự của từng cluster - Tối ưu cho biểu đồ
        """
        try:
            # Sử dụng actual_clusters (extended_clusters) để đảm bảo consistency với dữ liệu đã ghi
            # actual_clusters đã được chuẩn hóa trong prepare_row_data để có đủ 6 phần tử

            # Lấy dữ liệu % từ dòng trước đó để tính Delta
            previous_row = current_row - 1
            previous_percentage_response = await self.execute_with_retry(
                self.service.spreadsheets().values().get(
                    spreadsheetId=self.spreadsheet_id,
                    range=f"'Historical Data'!J{previous_row}:O{previous_row}"  # % dòng trước (J-O)
                ),
                "Lỗi khi lấy dữ liệu % dòng trước"
            )

            if not previous_percentage_response or 'values' not in previous_percentage_response or not previous_percentage_response['values']:
                self.log("Không có dữ liệu % dòng trước để so sánh")
                return

            previous_percentage_data = previous_percentage_response['values'][0]

            # Lấy dữ liệu % hiện tại để tính Delta
            current_percentage_response = await self.execute_with_retry(
                self.service.spreadsheets().values().get(
                    spreadsheetId=self.spreadsheet_id,
                    range=f"'Historical Data'!J{current_row}:O{current_row}"  # % hiện tại (J-O)
                ),
                "Lỗi khi lấy dữ liệu % hiện tại"
            )

            if not current_percentage_response or 'values' not in current_percentage_response or not current_percentage_response['values']:
                self.log("Không có dữ liệu % hiện tại")
                return

            current_percentage_data = current_percentage_response['values'][0]

            # Parse dữ liệu % từ dòng trước (cột J-O) - sử dụng actual_clusters
            previous_percentages = {}
            for i in range(6):  # Luôn xử lý đủ 6 cột
                cluster = actual_clusters[i] if i < len(actual_clusters) else f"Cluster {i+1}"

                # Lấy % từ dòng trước - trực tiếp từ previous_percentage_data (đã là cột J-O)
                if i < len(previous_percentage_data):
                    try:
                        # Kiểm tra format dữ liệu trong sheet
                        raw_value = previous_percentage_data[i]
                        if not raw_value:
                            prev_percentage = 0
                        else:
                            # Dữ liệu trong sheet được ghi ở dạng "47.5%" (string)
                            if isinstance(raw_value, str):
                                # Remove % và parse thành float
                                clean_value = raw_value.replace('%', '').strip()
                                prev_percentage = float(clean_value) if clean_value else 0
                            else:
                                # Fallback cho trường hợp khác
                                float_val = float(raw_value)
                                if float_val <= 1.0:  # Decimal format
                                    prev_percentage = float_val * 100
                                else:  # Already percentage
                                    prev_percentage = float_val

                        previous_percentages[cluster] = prev_percentage
                        print(f"[DEBUG] Previous {cluster}: {raw_value} → {prev_percentage}%")
                    except Exception as e:
                        print(f"[DEBUG] Error parsing previous {cluster}: {e}")
                        previous_percentages[cluster] = 0
                else:
                    previous_percentages[cluster] = 0

            # Parse dữ liệu % hiện tại (cột J-O) - sử dụng actual_clusters
            current_percentages_parsed = {}
            for i in range(6):  # Luôn xử lý đủ 6 cột
                cluster = actual_clusters[i] if i < len(actual_clusters) else f"Cluster {i+1}"

                if i < len(current_percentage_data):
                    try:
                        # Kiểm tra format dữ liệu trong sheet
                        raw_value = current_percentage_data[i]
                        if not raw_value:
                            curr_percentage = 0
                        else:
                            # Dữ liệu trong sheet được ghi ở dạng "47.5%" (string)
                            if isinstance(raw_value, str):
                                # Remove % và parse thành float
                                clean_value = raw_value.replace('%', '').strip()
                                curr_percentage = float(clean_value) if clean_value else 0
                            else:
                                # Fallback cho trường hợp khác
                                float_val = float(raw_value)
                                if float_val <= 1.0:  # Decimal format
                                    curr_percentage = float_val * 100
                                else:  # Already percentage
                                    curr_percentage = float_val

                        current_percentages_parsed[cluster] = curr_percentage
                        print(f"[DEBUG] Current {cluster}: {raw_value} → {curr_percentage}%")
                    except Exception as e:
                        print(f"[DEBUG] Error parsing current {cluster}: {e}")
                        current_percentages_parsed[cluster] = 0
                else:
                    current_percentages_parsed[cluster] = 0

            # Tính toán Delta (chênh lệch % market share) giữa các lần scrape - sử dụng actual_clusters
            deltas = []
            trend_indicators = []

            for i in range(6):  # Luôn tính đủ 6 cột để biểu đồ không bị lỗi
                cluster = actual_clusters[i] if i < len(actual_clusters) else f"Cluster {i+1}"
                current_percentage = current_percentages_parsed.get(cluster, 0)
                previous_percentage = previous_percentages.get(cluster, 0)

                # Tính Delta (chênh lệch % market share giữa 2 lần scrape)
                delta = current_percentage - previous_percentage

                # Debug logging chi tiết
                print(f"[DEBUG] {cluster}: {previous_percentage}% → {current_percentage}% = Δ{delta:.2f}%")

                # Xác định xu hướng dựa trên Delta % (thay vì doanh thu)
                if delta > 5:  # Tăng mạnh > 5%
                    trend_indicator = "🚀 STRONG+"
                elif delta > 2:  # Tăng vừa > 2%
                    trend_indicator = "📈 UP"
                elif delta > 0.1:  # Tăng nhẹ > 0.1%
                    trend_indicator = "↗️ RISE"
                elif delta < -5:  # Giảm mạnh < -5%
                    trend_indicator = "📉 STRONG-"
                elif delta < -0.1:  # Giảm > -0.1%
                    trend_indicator = "↘️ DOWN"
                else:  # Không đổi hoặc thay đổi rất nhỏ
                    trend_indicator = "➡️ FLAT"

                # Lưu Delta % thuần túy để line chart có thể vẽ được (không có ký tự đặc biệt)
                deltas.append(round(delta, 2))  # Làm tròn 2 chữ số thập phân
                trend_indicators.append(trend_indicator)

            # Đảm bảo luôn có đủ 6 cột cho biểu đồ (không cần while loop vì đã loop 6 lần)

            # Debug: Kiểm tra dữ liệu Delta trước khi ghi (chỉ hiển thị trong Terminal)
            print(f"[DEBUG] Deltas = {deltas}")
            print(f"[DEBUG] Trend indicators = {trend_indicators}")

            # Thêm dữ liệu xu hướng vào cột P-U (deltas) và V-AA (trend indicators)
            trend_range_deltas = f"'Historical Data'!P{current_row}:U{current_row}"
            trend_range_symbols = f"'Historical Data'!V{current_row}:AA{current_row}"

            print(f"[DEBUG] Deltas = {deltas}")
            print(f"[DEBUG] Trend indicators = {trend_indicators}")
            print(f"[DEBUG] Ghi Delta vào range {trend_range_deltas}")
            print(f"[DEBUG] Ghi Trend vào range {trend_range_symbols}")

            # Cập nhật deltas với RAW input để đảm bảo là số
            await self.execute_with_retry(
                self.service.spreadsheets().values().update(
                    spreadsheetId=self.spreadsheet_id,
                    range=trend_range_deltas,
                    valueInputOption="RAW",  # Đảm bảo ghi số thô, không format
                    body={"values": [deltas]}
                ),
                "Lỗi khi thêm deltas"
            )

            # Cập nhật trend indicators
            await self.execute_with_retry(
                self.service.spreadsheets().values().update(
                    spreadsheetId=self.spreadsheet_id,
                    range=trend_range_symbols,
                    valueInputOption="USER_ENTERED",
                    body={"values": [trend_indicators]}
                ),
                "Lỗi khi thêm trend indicators"
            )

            # Format cột Delta để hiển thị số với 1 chữ số thập phân
            await self.format_delta_columns(historical_sheet_id, current_row)

            self.log("✅ Đã tính Delta cho Line chart")

        except Exception as e:
            self.log(f"❌ Lỗi khi tính toán xu hướng: {e}")
            import traceback
            self.log(traceback.format_exc())

    async def format_delta_columns(self, historical_sheet_id, current_row):
        """
        Format cột Delta để hiển thị số với 1 chữ số thập phân và màu sắc
        """
        try:
            # Format cột Delta (P-U) với số thập phân và màu sắc
            format_request = {
                'repeatCell': {
                    'range': {
                        'sheetId': historical_sheet_id,
                        'startRowIndex': current_row - 1,  # Chỉ format dòng hiện tại
                        'endRowIndex': current_row,
                        'startColumnIndex': 15,  # Cột P
                        'endColumnIndex': 21     # Cột U
                    },
                    'cell': {
                        'userEnteredFormat': {
                            'numberFormat': {
                                'type': 'NUMBER',
                                'pattern': '#,##0.0'  # Hiển thị 1 chữ số thập phân
                            },
                            'horizontalAlignment': 'CENTER'
                        }
                    },
                    'fields': 'userEnteredFormat(numberFormat,horizontalAlignment)'
                }
            }

            await self.execute_with_retry(
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': [format_request]}
                ),
                "Lỗi khi format cột Delta"
            )

            self.log("✅ Đã format cột Delta")

        except Exception as e:
            self.log(f"❌ Lỗi khi format cột Delta: {e}")

    async def update_timeline_headers_if_needed(self, historical_sheet_id, actual_clusters):
        """
        Cập nhật header timeline để phù hợp với clusters thực tế
        """
        try:
            # Tạo header động dựa trên clusters thực tế
            headers = ["Timestamp", "Thời gian"]

            # Thêm tên clusters thực tế cho doanh thu
            for cluster in actual_clusters:
                headers.append(cluster)

            # Đảm bảo có đủ 6 cột cho doanh thu
            while len(headers) < 8:  # 2 + 6
                headers.append("Cluster " + str(len(headers) - 1))

            headers.append("")  # Separator

            # Thêm tên clusters thực tế cho phần trăm
            for cluster in actual_clusters:
                headers.append(f"% {cluster}")

            # Đảm bảo có đủ 6 cột cho phần trăm
            while len(headers) < 15:  # 2 + 6 + 1 + 6
                headers.append(f"% Cluster {len(headers) - 8}")

            # Thêm cột Delta (chênh lệch doanh thu, đơn vị K VNĐ)
            for cluster in actual_clusters:
                headers.append(f"Δ {cluster} (K)")

            # Đảm bảo có đủ 6 cột cho delta
            while len(headers) < 21:  # 15 + 6
                headers.append(f"Δ {len(headers) - 14} (K)")

            # Thêm cột chỉ báo xu hướng (Trend Indicator)
            for cluster in actual_clusters:
                headers.append(f"Trend {cluster}")

            # Đảm bảo có đủ 6 cột cho trend indicator
            while len(headers) < 27:  # 21 + 6
                headers.append(f"Trend {len(headers) - 20}")

            self.log(f"Cập nhật header với clusters và xu hướng: {actual_clusters}")

            # Cập nhật header
            header_request = {
                'updateCells': {
                    'range': {
                        'sheetId': historical_sheet_id,
                        'startRowIndex': 0,
                        'endRowIndex': 1,
                        'startColumnIndex': 0,
                        'endColumnIndex': len(headers)
                    },
                    'rows': [
                        {
                            'values': [{'userEnteredValue': {'stringValue': header}} for header in headers]
                        }
                    ],
                    'fields': 'userEnteredValue'
                }
            }

            await self.execute_with_retry(
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': [header_request]}
                ),
                "Lỗi khi cập nhật timeline headers"
            )

            self.log("✅ Đã cập nhật timeline headers với clusters thực tế")

        except Exception as e:
            self.log(f"❌ Lỗi khi cập nhật timeline headers: {e}")

    # DEPRECATED: Hàm cũ đã được thay thế bằng logic tối ưu trong update_historical_data_optimized()
    # async def create_timeline_dashboard(self, historical_sheet_id, clusters_data):
    #     """
    #     Tạo dashboard với biểu đồ timeline cho Historical Data
    #     """
        try:
            self.log("Tạo timeline dashboard...")

            # Lấy số dòng dữ liệu để tính vị trí header
            response = await self.execute_with_retry(
                self.service.spreadsheets().values().get(
                    spreadsheetId=self.spreadsheet_id,
                    range="'Historical Data'!A:A"
                ),
                "Lỗi khi đếm dòng dữ liệu cho header"
            )

            # Không cần đếm data_rows nữa vì sử dụng vị trí cố định

            # Đặt header ở vị trí cố định để tránh xung đột với dữ liệu
            header_start_row = 35
            print(f"[DEBUG] Header sẽ được đặt ở dòng {header_start_row + 1}")

            # Tạo tiêu đề dashboard
            title_request = {
                'updateCells': {
                    'range': {
                        'sheetId': historical_sheet_id,
                        'startRowIndex': header_start_row,
                        'endRowIndex': header_start_row + 2,
                        'startColumnIndex': 0,  # Cột A
                        'endColumnIndex': 9
                    },
                    'rows': [
                        {
                            'values': [
                                {'userEnteredValue': {'stringValue': 'CLUSTER TIMELINE ANALYSIS'}},
                                {'userEnteredValue': {'stringValue': ''}},
                                {'userEnteredValue': {'stringValue': ''}},
                                {'userEnteredValue': {'stringValue': ''}},
                                {'userEnteredValue': {'stringValue': ''}},
                                {'userEnteredValue': {'stringValue': ''}},
                                {'userEnteredValue': {'stringValue': f'Updated: {datetime.now().strftime("%H:%M:%S")}'}},
                            ]
                        },
                        {
                            'values': [
                                {'userEnteredValue': {'stringValue': 'Theo dõi xu hướng cluster theo khung giờ'}},
                            ]
                        }
                    ],
                    'fields': 'userEnteredValue'
                }
            }

            await self.execute_with_retry(
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': [title_request]}
                ),
                "Lỗi khi tạo tiêu đề timeline dashboard"
            )

            # Lấy số lần scrape để quyết định có tạo biểu đồ hay không (chỉ đếm trong vùng dữ liệu)
            response = await self.execute_with_retry(
                self.service.spreadsheets().values().get(
                    spreadsheetId=self.spreadsheet_id,
                    range="'Historical Data'!A2:A29"  # Chỉ kiểm tra vùng dữ liệu thực sự
                ),
                "Lỗi khi đếm số lần scrape"
            )

            scrape_count = 1
            if response and 'values' in response:
                # Đếm số dòng có dữ liệu thực sự
                actual_scrapes = 0
                for row in response['values']:
                    if row and row[0].strip():  # Có timestamp
                        actual_scrapes += 1
                scrape_count = max(1, actual_scrapes)

            print(f"[DEBUG] Scrape count: {scrape_count}")

            # Kiểm tra xem đã có biểu đồ chưa
            existing_charts = await self.check_existing_charts(historical_sheet_id)

            if not existing_charts:
                # Chưa có biểu đồ nào: Tạo biểu đồ lần đầu
                print("[DEBUG] Chưa có biểu đồ, tạo biểu đồ lần đầu")
                await self.create_cluster_pie_chart(historical_sheet_id)

                # Nếu đã có đủ dữ liệu cho line chart (từ scrape 3 trở đi)
                if scrape_count >= 3:
                    await self.create_percentage_timeline_chart(historical_sheet_id)
                    await self.create_or_update_revenue_timeline_chart(historical_sheet_id)
                    print("[DEBUG] Đã tạo pie chart, Delta line chart và Revenue line chart")
                else:
                    print("[DEBUG] Chỉ tạo pie chart, chờ thêm dữ liệu cho line charts")
            elif scrape_count == 3 and len(existing_charts) == 1:
                # Đã có pie chart, thêm line charts khi có đủ dữ liệu
                print("[DEBUG] Thêm line charts khi có đủ dữ liệu Delta")
                await self.create_percentage_timeline_chart(historical_sheet_id)
                await self.create_or_update_revenue_timeline_chart(historical_sheet_id)
            else:
                # Đã có biểu đồ: Cập nhật dữ liệu Pie chart với dữ liệu mới nhất
                print(f"[DEBUG] Lần scrape thứ {scrape_count}: Cập nhật dữ liệu Pie chart")
                await self.update_pie_chart_data(historical_sheet_id)

            self.log("✅ Đã tạo timeline dashboard thành công")

        except Exception as e:
            self.log(f"❌ Lỗi khi tạo timeline dashboard: {e}")

    async def update_pie_chart_data(self, historical_sheet_id):
        """
        Cập nhật vùng dữ liệu cho Pie chart (dòng 31-32) với dữ liệu mới nhất
        """
        try:
            # Lấy dữ liệu mới nhất từ vùng dữ liệu chính
            response = await self.execute_with_retry(
                self.service.spreadsheets().values().get(
                    spreadsheetId=self.spreadsheet_id,
                    range="'Historical Data'!A2:O29"  # Vùng dữ liệu chính
                ),
                "Lỗi khi lấy dữ liệu cho Pie chart"
            )

            if not response or 'values' not in response:
                return

            # Tìm dòng cuối cùng có dữ liệu
            values = response['values']
            last_data_row = None
            for row in reversed(values):
                if row and len(row) > 0 and row[0].strip():  # Có timestamp
                    last_data_row = row
                    break

            if not last_data_row:
                return

            # Tạo header cho Pie chart (dòng 31)
            pie_header = ["Cluster 1", "Cluster 2", "Cluster 3", "Cluster 4", "Cluster 5", "Cluster 6"]

            # Lấy dữ liệu % từ dòng cuối cùng (cột J-O, index 9-14)
            pie_data = []
            for i in range(9, 15):  # Cột J đến O
                if i < len(last_data_row):
                    # Chuyển đổi từ string "X.X%" thành số
                    value_str = str(last_data_row[i]).replace('%', '').strip()
                    try:
                        pie_data.append(float(value_str))
                    except:
                        pie_data.append(0.0)
                else:
                    pie_data.append(0.0)

            # Ghi dữ liệu vào dòng 31-32
            await self.execute_with_retry(
                self.service.spreadsheets().values().update(
                    spreadsheetId=self.spreadsheet_id,
                    range="'Historical Data'!A31:F32",
                    valueInputOption="USER_ENTERED",
                    body={"values": [pie_header, pie_data]}
                ),
                "Lỗi khi cập nhật dữ liệu Pie chart"
            )

            print(f"[DEBUG] Đã cập nhật dữ liệu Pie chart với dữ liệu mới nhất: {pie_data}")

        except Exception as e:
            print(f"[DEBUG] ❌ Lỗi khi cập nhật dữ liệu Pie chart: {e}")

    async def check_existing_charts(self, historical_sheet_id):
        """
        Kiểm tra xem đã có biểu đồ nào trong Historical Data sheet chưa
        Returns: List of existing chart IDs
        """
        try:
            # Lấy thông tin sheet để tìm các biểu đồ hiện có
            sheet_metadata = await self.execute_with_retry(
                self.service.spreadsheets().get(spreadsheetId=self.spreadsheet_id),
                "Lỗi khi lấy thông tin sheet"
            )

            existing_charts = []
            for sheet in sheet_metadata.get('sheets', []):
                if sheet['properties']['sheetId'] == historical_sheet_id:
                    # Tìm tất cả biểu đồ trong sheet này
                    for chart in sheet.get('charts', []):
                        existing_charts.append(chart['chartId'])

            print(f"[DEBUG] Tìm thấy {len(existing_charts)} biểu đồ hiện có")
            return existing_charts

        except Exception as e:
            print(f"[DEBUG] ❌ Lỗi khi kiểm tra biểu đồ hiện có: {e}")
            return []

    async def clear_existing_charts(self, historical_sheet_id):
        """
        Xóa tất cả biểu đồ hiện có trong Historical Data sheet để tránh chồng lấp
        """
        try:
            print("[DEBUG] Đang kiểm tra và xóa biểu đồ cũ...")

            # Lấy thông tin sheet để tìm các biểu đồ hiện có
            sheet_metadata = await self.execute_with_retry(
                self.service.spreadsheets().get(spreadsheetId=self.spreadsheet_id),
                "Lỗi khi lấy thông tin sheet"
            )

            charts_to_delete = []
            for sheet in sheet_metadata.get('sheets', []):
                if sheet['properties']['sheetId'] == historical_sheet_id:
                    # Tìm tất cả biểu đồ trong sheet này
                    for chart in sheet.get('charts', []):
                        charts_to_delete.append(chart['chartId'])

            if charts_to_delete:
                print(f"[DEBUG] Tìm thấy {len(charts_to_delete)} biểu đồ cũ, đang xóa...")

                # Tạo request xóa tất cả biểu đồ
                delete_requests = []
                for chart_id in charts_to_delete:
                    delete_requests.append({
                        'deleteEmbeddedObject': {
                            'objectId': chart_id
                        }
                    })

                # Thực hiện xóa
                await self.execute_with_retry(
                    self.service.spreadsheets().batchUpdate(
                        spreadsheetId=self.spreadsheet_id,
                        body={'requests': delete_requests}
                    ),
                    "Lỗi khi xóa biểu đồ cũ"
                )

                print(f"[DEBUG] ✅ Đã xóa {len(charts_to_delete)} biểu đồ cũ")
            else:
                print("[DEBUG] Không có biểu đồ cũ nào cần xóa")

        except Exception as e:
            print(f"[DEBUG] ❌ Lỗi khi xóa biểu đồ cũ: {e}")
            # Không throw exception để không ảnh hưởng đến quá trình tạo biểu đồ mới

    async def create_cluster_pie_chart(self, historical_sheet_id):
        """
        Tạo biểu đồ pie chart cho phân bố % cluster từ dữ liệu mới nhất - Tối ưu chống lỗi N/A
        Sử dụng vùng dữ liệu cố định ở dòng 31-32 cho Pie chart
        """
        try:
            self.log("Tạo biểu đồ pie chart phân bố cluster...")

            # Tạo vùng dữ liệu cho Pie chart ở dòng 31-32
            await self.update_pie_chart_data(historical_sheet_id)

            # Validate dữ liệu trước khi tạo biểu đồ
            pie_data_response = await self.execute_with_retry(
                self.service.spreadsheets().values().get(
                    spreadsheetId=self.spreadsheet_id,
                    range="'Historical Data'!A31:F32"  # Header và data cho pie chart
                ),
                "Lỗi khi lấy dữ liệu pie chart"
            )

            # Kiểm tra dữ liệu hợp lệ
            is_valid, clean_data = self.validate_chart_data(pie_data_response, "Pie Chart")
            if not is_valid:
                self.log("⚠️ Bỏ qua tạo Pie Chart do dữ liệu không hợp lệ")
                return

            # Tạo pie chart từ dữ liệu % cluster (cột J-O)
            pie_chart_request = {
                'addChart': {
                    'chart': {
                        'spec': {
                            'title': 'Phân bố % Cluster (Dữ liệu mới nhất)',
                            'pieChart': {
                                'legendPosition': 'RIGHT_LEGEND',
                                'domain': {
                                    'sourceRange': {
                                        'sources': [
                                            {
                                                'sheetId': historical_sheet_id,
                                                'startRowIndex': 30,  # Dòng 31 (header cho Pie chart)
                                                'endRowIndex': 31,
                                                'startColumnIndex': 0,  # Cột A (Cluster names)
                                                'endColumnIndex': 6   # Cột F
                                            }
                                        ]
                                    }
                                },
                                'series': {
                                    'sourceRange': {
                                        'sources': [
                                            {
                                                'sheetId': historical_sheet_id,
                                                'startRowIndex': 31,   # Dòng 32 (dữ liệu % cho Pie chart)
                                                'endRowIndex': 32,
                                                'startColumnIndex': 0,  # Cột A (% values)
                                                'endColumnIndex': 6   # Cột F
                                            }
                                        ]
                                    }
                                }
                            }
                        },
                        'position': {
                            'overlayPosition': {
                                'anchorCell': {
                                    'sheetId': historical_sheet_id,
                                    'rowIndex': 5,   # Dòng 6
                                    'columnIndex': 15  # Cột P
                                },
                                'widthPixels': 600,
                                'heightPixels': 400
                            }
                        }
                    }
                }
            }

            # Tạo pie chart với timeout cao
            self.log("🎨 Đang tạo pie chart...")
            await self.execute_with_retry(
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': [pie_chart_request]}
                ),
                "Lỗi khi tạo pie chart cluster",
                max_attempts=3,
                initial_delay=5
            )

            self.log("✅ Đã tạo pie chart phân bố cluster")

        except Exception as e:
            self.log(f"❌ Lỗi khi tạo pie chart cluster: {e}")

    async def create_revenue_timeline_chart(self, historical_sheet_id):
        """
        Tạo biểu đồ line chart cho xu hướng doanh thu theo thời gian
        """
        try:
            self.log("Tạo biểu đồ xu hướng doanh thu...")

            # Lấy số dòng dữ liệu hiện có
            response = await self.execute_with_retry(
                self.service.spreadsheets().values().get(
                    spreadsheetId=self.spreadsheet_id,
                    range="'Historical Data'!A:A"
                ),
                "Lỗi khi đếm dòng dữ liệu"
            )

            # Tính toán vị trí biểu đồ - đặt ở vị trí cố định để tránh xung đột
            chart_row_position = 40

            # Tạo line chart cho doanh thu - sử dụng cách đơn giản hơn
            revenue_chart_request = {
                'addChart': {
                    'chart': {
                        'spec': {
                            'title': 'NMV từng khung giờ',
                            'basicChart': {
                                'chartType': 'LINE',
                                'legendPosition': 'RIGHT_LEGEND',
                                'headerCount': 1,
                                'axis': [
                                    {
                                        'position': 'BOTTOM_AXIS',
                                        'title': 'Thời gian'
                                    },
                                    {
                                        'position': 'LEFT_AXIS',
                                        'title': 'Doanh thu'
                                    }
                                ],
                                'domains': [
                                    {
                                        'domain': {
                                            'sourceRange': {
                                                'sources': [
                                                    {
                                                        'sheetId': historical_sheet_id,
                                                        'startRowIndex': 0,  # Bao gồm header
                                                        'endRowIndex': 29,
                                                        'startColumnIndex': 1,  # Cột B (Thời gian)
                                                        'endColumnIndex': 2
                                                    }
                                                ]
                                            }
                                        }
                                    }
                                ],
                                'series': [
                                    {
                                        'series': {
                                            'sourceRange': {
                                                'sources': [
                                                    {
                                                        'sheetId': historical_sheet_id,
                                                        'startRowIndex': 0,  # Bao gồm header
                                                        'endRowIndex': 29,
                                                        'startColumnIndex': 2,  # Cột C - Cluster 1
                                                        'endColumnIndex': 3
                                                    }
                                                ]
                                            }
                                        },
                                        'targetAxis': 'LEFT_AXIS'
                                    },
                                    {
                                        'series': {
                                            'sourceRange': {
                                                'sources': [
                                                    {
                                                        'sheetId': historical_sheet_id,
                                                        'startRowIndex': 0,
                                                        'endRowIndex': 29,
                                                        'startColumnIndex': 3,  # Cột D - Cluster 2
                                                        'endColumnIndex': 4
                                                    }
                                                ]
                                            }
                                        },
                                        'targetAxis': 'LEFT_AXIS'
                                    },
                                    {
                                        'series': {
                                            'sourceRange': {
                                                'sources': [
                                                    {
                                                        'sheetId': historical_sheet_id,
                                                        'startRowIndex': 0,
                                                        'endRowIndex': 29,
                                                        'startColumnIndex': 4,  # Cột E - Cluster 3
                                                        'endColumnIndex': 5
                                                    }
                                                ]
                                            }
                                        },
                                        'targetAxis': 'LEFT_AXIS'
                                    },
                                    {
                                        'series': {
                                            'sourceRange': {
                                                'sources': [
                                                    {
                                                        'sheetId': historical_sheet_id,
                                                        'startRowIndex': 0,
                                                        'endRowIndex': 29,
                                                        'startColumnIndex': 5,  # Cột F - Cluster 4
                                                        'endColumnIndex': 6
                                                    }
                                                ]
                                            }
                                        },
                                        'targetAxis': 'LEFT_AXIS'
                                    },
                                    {
                                        'series': {
                                            'sourceRange': {
                                                'sources': [
                                                    {
                                                        'sheetId': historical_sheet_id,
                                                        'startRowIndex': 0,
                                                        'endRowIndex': 29,
                                                        'startColumnIndex': 6,  # Cột G - Cluster 5
                                                        'endColumnIndex': 7
                                                    }
                                                ]
                                            }
                                        },
                                        'targetAxis': 'LEFT_AXIS'
                                    },
                                    {
                                        'series': {
                                            'sourceRange': {
                                                'sources': [
                                                    {
                                                        'sheetId': historical_sheet_id,
                                                        'startRowIndex': 0,
                                                        'endRowIndex': 29,
                                                        'startColumnIndex': 7,  # Cột H - Cluster 6
                                                        'endColumnIndex': 8
                                                    }
                                                ]
                                            }
                                        },
                                        'targetAxis': 'LEFT_AXIS'
                                    }
                                ]
                            }
                        },
                        'position': {
                            'overlayPosition': {
                                'anchorCell': {
                                    'sheetId': historical_sheet_id,
                                    'rowIndex': chart_row_position,   # Vị trí động
                                    'columnIndex': 30  # Cột AE (tránh xung đột với Delta)
                                },
                                'widthPixels': 800,
                                'heightPixels': 400
                            }
                        }
                    }
                }
            }

            await self.execute_with_retry(
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': [revenue_chart_request]}
                ),
                "Lỗi khi tạo biểu đồ doanh thu timeline"
            )

            self.log("✅ Đã tạo biểu đồ xu hướng doanh thu")

        except Exception as e:
            self.log(f"❌ Lỗi khi tạo biểu đồ doanh thu timeline: {e}")

    async def create_percentage_timeline_chart(self, historical_sheet_id):
        """
        Tạo biểu đồ line chart cho xu hướng thay đổi Delta cluster theo thời gian - Tối ưu chống lỗi N/A
        """
        try:
            self.log("Tạo biểu đồ xu hướng Delta cluster theo thời gian...")

            # Validate dữ liệu Delta trước khi tạo biểu đồ
            delta_data_response = await self.execute_with_retry(
                self.service.spreadsheets().values().get(
                    spreadsheetId=self.spreadsheet_id,
                    range="'Historical Data'!A1:U29"  # Timestamp + Delta data
                ),
                "Lỗi khi lấy dữ liệu Delta cho line chart"
            )

            # Kiểm tra dữ liệu hợp lệ
            is_valid, clean_data = self.validate_chart_data(delta_data_response, "Line Chart Delta")
            if not is_valid:
                self.log("⚠️ Bỏ qua tạo Line Chart do dữ liệu Delta không hợp lệ")
                return

            # Tính toán vị trí biểu đồ - đặt ở dòng 35+ để tránh xung đột
            chart_row_position = 35

            # Tạo line chart cho xu hướng % cluster
            percentage_line_chart_request = {
                'addChart': {
                    'chart': {
                        'spec': {
                            'title': 'Delta Doanh thu Cluster theo khung giờ',
                            'basicChart': {
                                'chartType': 'LINE',
                                'legendPosition': 'RIGHT_LEGEND',
                                'headerCount': 1,
                                'axis': [
                                    {
                                        'position': 'BOTTOM_AXIS',
                                        'title': 'Thời gian'
                                    },
                                    {
                                        'position': 'LEFT_AXIS',
                                        'title': 'Chênh lệch doanh thu (K VNĐ)'
                                    }
                                ],
                                'domains': [
                                    {
                                        'domain': {
                                            'sourceRange': {
                                                'sources': [
                                                    {
                                                        'sheetId': historical_sheet_id,
                                                        'startRowIndex': 2,  # Từ dòng 3 (bỏ qua header và dòng đầu tiên không có Delta)
                                                        'endRowIndex': 29,  # Sử dụng phạm vi cố định để biểu đồ tự động cập nhật
                                                        'startColumnIndex': 1,  # Cột B (Thời gian)
                                                        'endColumnIndex': 2
                                                    }
                                                ]
                                            }
                                        }
                                    }
                                ],
                                'series': [
                                    {
                                        'series': {
                                            'sourceRange': {
                                                'sources': [
                                                    {
                                                        'sheetId': historical_sheet_id,
                                                        'startRowIndex': 2,  # Từ dòng 3 (bỏ qua header và dòng đầu tiên không có Delta)
                                                        'endRowIndex': 29,  # Sử dụng phạm vi cố định để biểu đồ tự động cập nhật
                                                        'startColumnIndex': 15,  # Cột P - Delta Cluster 1
                                                        'endColumnIndex': 16
                                                    }
                                                ]
                                            }
                                        },
                                        'targetAxis': 'LEFT_AXIS'
                                    },
                                    {
                                        'series': {
                                            'sourceRange': {
                                                'sources': [
                                                    {
                                                        'sheetId': historical_sheet_id,
                                                        'startRowIndex': 2,
                                                        'endRowIndex': 29,
                                                        'startColumnIndex': 16,  # Cột Q - Delta Cluster 2
                                                        'endColumnIndex': 17
                                                    }
                                                ]
                                            }
                                        },
                                        'targetAxis': 'LEFT_AXIS'
                                    },
                                    {
                                        'series': {
                                            'sourceRange': {
                                                'sources': [
                                                    {
                                                        'sheetId': historical_sheet_id,
                                                        'startRowIndex': 2,
                                                        'endRowIndex': 29,
                                                        'startColumnIndex': 17,  # Cột R - Delta Cluster 3
                                                        'endColumnIndex': 18
                                                    }
                                                ]
                                            }
                                        },
                                        'targetAxis': 'LEFT_AXIS'
                                    },
                                    {
                                        'series': {
                                            'sourceRange': {
                                                'sources': [
                                                    {
                                                        'sheetId': historical_sheet_id,
                                                        'startRowIndex': 2,
                                                        'endRowIndex': 29,
                                                        'startColumnIndex': 18,  # Cột S - Delta Cluster 4
                                                        'endColumnIndex': 19
                                                    }
                                                ]
                                            }
                                        },
                                        'targetAxis': 'LEFT_AXIS'
                                    },
                                    {
                                        'series': {
                                            'sourceRange': {
                                                'sources': [
                                                    {
                                                        'sheetId': historical_sheet_id,
                                                        'startRowIndex': 2,
                                                        'endRowIndex': 29,
                                                        'startColumnIndex': 19,  # Cột T - Delta Cluster 5
                                                        'endColumnIndex': 20
                                                    }
                                                ]
                                            }
                                        },
                                        'targetAxis': 'LEFT_AXIS'
                                    },
                                    {
                                        'series': {
                                            'sourceRange': {
                                                'sources': [
                                                    {
                                                        'sheetId': historical_sheet_id,
                                                        'startRowIndex': 2,
                                                        'endRowIndex': 29,
                                                        'startColumnIndex': 20,  # Cột U - Delta Cluster 6
                                                        'endColumnIndex': 21
                                                    }
                                                ]
                                            }
                                        },
                                        'targetAxis': 'LEFT_AXIS'
                                    }
                                ]
                            }
                        },
                        'position': {
                            'overlayPosition': {
                                'anchorCell': {
                                    'sheetId': historical_sheet_id,
                                    'rowIndex': chart_row_position + 20,   # Dưới biểu đồ đầu tiên
                                    'columnIndex': 30  # Cột AE (tránh xung đột với Delta)
                                },
                                'widthPixels': 800,
                                'heightPixels': 400
                            }
                        }
                    }
                }
            }

            await self.execute_with_retry(
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': [percentage_line_chart_request]}
                ),
                "Lỗi khi tạo line chart xu hướng %"
            )

            self.log("✅ Đã tạo line chart xu hướng % cluster")

        except Exception as e:
            self.log(f"❌ Lỗi khi tạo line chart xu hướng %: {e}")

    async def create_or_update_revenue_timeline_chart(self, historical_sheet_id):
        """
        Tạo biểu đồ Line Chart theo dõi doanh thu tuyệt đối của từng cluster theo thời gian
        """
        try:
            self.log("Tạo/cập nhật biểu đồ doanh thu timeline...")

            # Validate dữ liệu doanh thu trước khi tạo biểu đồ
            revenue_data_response = await self.execute_with_retry(
                self.service.spreadsheets().values().get(
                    spreadsheetId=self.spreadsheet_id,
                    range="'Historical Data'!A1:H29"  # Timestamp + Revenue data (C-H)
                ),
                "Lỗi khi lấy dữ liệu doanh thu cho timeline chart"
            )

            # Kiểm tra dữ liệu hợp lệ
            is_valid, clean_data = self.validate_chart_data(revenue_data_response, "Revenue Timeline Chart")
            if not is_valid:
                self.log("⚠️ Bỏ qua tạo Revenue Timeline Chart do dữ liệu không hợp lệ")
                return

            # Kiểm tra xem đã có biểu đồ doanh thu timeline chưa
            existing_charts = await self.get_existing_charts(historical_sheet_id)
            revenue_chart_exists = any("Doanh thu Cluster theo thời gian" in str(chart) for chart in existing_charts)

            if not revenue_chart_exists:
                # Tạo biểu đồ mới
                await self.create_revenue_timeline_chart_new(historical_sheet_id)
                self.log("✅ Đã tạo biểu đồ doanh thu timeline mới")
            else:
                # Biểu đồ đã tồn tại, dữ liệu sẽ tự động cập nhật
                self.log("✅ Biểu đồ doanh thu timeline đã tồn tại, dữ liệu tự động cập nhật")

        except Exception as e:
            self.log(f"❌ Lỗi khi tạo/cập nhật revenue timeline chart: {e}")

    async def create_revenue_timeline_chart_new(self, historical_sheet_id):
        """
        Tạo biểu đồ Line Chart mới cho doanh thu cluster theo thời gian
        """
        try:
            # Tạo line chart cho doanh thu cluster theo thời gian
            revenue_timeline_chart_request = {
                'addChart': {
                    'chart': {
                        'spec': {
                            'title': 'Doanh thu Cluster theo thời gian (VNĐ)',
                            'basicChart': {
                                'chartType': 'LINE',
                                'legendPosition': 'RIGHT_LEGEND',
                                'headerCount': 1,
                                'axis': [
                                    {
                                        'position': 'BOTTOM_AXIS',
                                        'title': 'Thời gian'
                                    },
                                    {
                                        'position': 'LEFT_AXIS',
                                        'title': 'Doanh thu (VNĐ)'
                                    }
                                ],
                                'domains': [
                                    {
                                        'domain': {
                                            'sourceRange': {
                                                'sources': [{
                                                    'sheetId': historical_sheet_id,
                                                    'startRowIndex': 0,  # Header
                                                    'endRowIndex': 29,   # Dữ liệu
                                                    'startColumnIndex': 1,  # Cột B (Thời gian)
                                                    'endColumnIndex': 2     # Chỉ cột thời gian
                                                }]
                                            }
                                        }
                                    }
                                ],
                                'series': []
                            }
                        },
                        'position': {
                            'overlayPosition': {
                                'anchorCell': {
                                    'sheetId': historical_sheet_id,
                                    'rowIndex': 40,  # Đặt ở dòng 41
                                    'columnIndex': 0  # Cột A
                                },
                                'widthPixels': 900,
                                'heightPixels': 450
                            }
                        }
                    }
                }
            }

            # Thêm series cho từng cluster (cột C-H)
            for i in range(6):
                cluster_name = f"Cluster {i+1}"  # Sẽ được cập nhật với tên thật
                series = {
                    'series': {
                        'sourceRange': {
                            'sources': [{
                                'sheetId': historical_sheet_id,
                                'startRowIndex': 0,  # Bao gồm header
                                'endRowIndex': 29,   # Dữ liệu
                                'startColumnIndex': 2 + i,  # Cột C, D, E, F, G, H
                                'endColumnIndex': 3 + i
                            }]
                        }
                    },
                    'targetAxis': 'LEFT_AXIS'
                }
                revenue_timeline_chart_request['addChart']['chart']['spec']['basicChart']['series'].append(series)

            # Tạo biểu đồ
            await self.execute_with_retry(
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': [revenue_timeline_chart_request]}
                ),
                "Lỗi khi tạo biểu đồ doanh thu timeline"
            )

            self.log("✅ Đã tạo biểu đồ doanh thu timeline")

        except Exception as e:
            self.log(f"❌ Lỗi khi tạo biểu đồ doanh thu timeline: {e}")

    def column_index_to_letter(self, index):
        """
        Chuyển đổi index cột (0-based) thành ký tự cột Excel (A, B, C, ..., Z, AA, AB, ...)
        """
        result = ""
        while index >= 0:
            result = chr(index % 26 + ord('A')) + result
            index = index // 26 - 1
        return result

    async def create_or_update_revenue_summary_table(self, historical_sheet_id, clusters, cluster_revenues, cluster_percentages):
        """
        Tạo/cập nhật bảng tóm tắt doanh thu hiện tại của từng cluster
        """
        try:
            self.log("Tạo/cập nhật bảng tóm tắt doanh thu...")

            # Vị trí bảng tóm tắt (điều chỉnh để tránh vượt quá giới hạn cột)
            summary_start_row = 35
            summary_start_col = 15  # Cột P thay vì Z để tránh vượt quá giới hạn

            # Tạo header cho bảng tóm tắt
            headers = [
                ["📊 TÓNG TẮT DOANH THU HIỆN TẠI"],
                [""],
                ["Cluster", "Doanh thu (VNĐ)", "% Thị phần", "Xu hướng"]
            ]

            # Tính tổng doanh thu
            total_revenue = sum(cluster_revenues.values())

            # Tạo dữ liệu cho bảng
            table_data = []
            for i, cluster in enumerate(clusters[:6]):
                revenue = cluster_revenues.get(cluster, 0)
                percentage = cluster_percentages.get(cluster, 0)

                # Format doanh thu
                if revenue >= 1000000000:  # >= 1 tỷ
                    revenue_formatted = f"{revenue/1000000000:.1f}B"
                elif revenue >= 1000000:  # >= 1 triệu
                    revenue_formatted = f"{revenue/1000000:.1f}M"
                elif revenue >= 1000:  # >= 1 nghìn
                    revenue_formatted = f"{revenue/1000:.1f}K"
                else:
                    revenue_formatted = f"{revenue:.0f}"

                # Xác định xu hướng (sẽ được cập nhật từ Delta data)
                trend = "➡️"  # Mặc định
                if percentage > 20:
                    trend = "🔥 HOT"
                elif percentage > 10:
                    trend = "📈 STRONG"
                elif percentage > 5:
                    trend = "↗️ GOOD"
                elif percentage < 1:
                    trend = "📉 WEAK"

                table_data.append([
                    cluster,
                    revenue_formatted,
                    f"{percentage:.1f}%",
                    trend
                ])

            # Thêm dòng tổng
            total_formatted = f"{total_revenue/1000000000:.1f}B" if total_revenue >= 1000000000 else f"{total_revenue/1000000:.1f}M"
            table_data.append(["", "", "", ""])  # Dòng trống
            table_data.append(["TỔNG", total_formatted, "100.0%", "📊"])

            # Gộp tất cả dữ liệu
            all_data = headers + table_data

            # Sử dụng hàm chuyển đổi cột an toàn
            start_col_letter = self.column_index_to_letter(summary_start_col)
            end_col_letter = self.column_index_to_letter(summary_start_col + 3)
            range_name = f"'Historical Data'!{start_col_letter}{summary_start_row + 1}:{end_col_letter}{summary_start_row + len(all_data)}"

            await self.execute_with_retry(
                self.service.spreadsheets().values().update(
                    spreadsheetId=self.spreadsheet_id,
                    range=range_name,
                    valueInputOption="USER_ENTERED",
                    body={"values": all_data}
                ),
                "Lỗi khi cập nhật bảng tóm tắt doanh thu"
            )

            # Format bảng tóm tắt
            await self.format_revenue_summary_table(historical_sheet_id, summary_start_row, summary_start_col, len(all_data))

            self.log("✅ Đã cập nhật bảng tóm tắt doanh thu")

        except Exception as e:
            self.log(f"❌ Lỗi khi tạo bảng tóm tắt doanh thu: {e}")

    async def format_revenue_summary_table(self, historical_sheet_id, start_row, start_col, num_rows):
        """
        Format bảng tóm tắt doanh thu
        """
        try:
            format_requests = []

            # Format header chính (dòng đầu)
            format_requests.append({
                'repeatCell': {
                    'range': {
                        'sheetId': historical_sheet_id,
                        'startRowIndex': start_row,
                        'endRowIndex': start_row + 1,
                        'startColumnIndex': start_col,
                        'endColumnIndex': start_col + 4
                    },
                    'cell': {
                        'userEnteredFormat': {
                            'backgroundColor': {'red': 0.2, 'green': 0.6, 'blue': 0.9},
                            'textFormat': {
                                'foregroundColor': {'red': 1, 'green': 1, 'blue': 1},
                                'fontSize': 12,
                                'bold': True
                            },
                            'horizontalAlignment': 'CENTER'
                        }
                    },
                    'fields': 'userEnteredFormat(backgroundColor,textFormat,horizontalAlignment)'
                }
            })

            # Format header cột (dòng 3)
            format_requests.append({
                'repeatCell': {
                    'range': {
                        'sheetId': historical_sheet_id,
                        'startRowIndex': start_row + 2,
                        'endRowIndex': start_row + 3,
                        'startColumnIndex': start_col,
                        'endColumnIndex': start_col + 4
                    },
                    'cell': {
                        'userEnteredFormat': {
                            'backgroundColor': {'red': 0.9, 'green': 0.9, 'blue': 0.9},
                            'textFormat': {
                                'fontSize': 10,
                                'bold': True
                            },
                            'horizontalAlignment': 'CENTER'
                        }
                    },
                    'fields': 'userEnteredFormat(backgroundColor,textFormat,horizontalAlignment)'
                }
            })

            # Thêm border cho toàn bộ bảng
            format_requests.append({
                'updateBorders': {
                    'range': {
                        'sheetId': historical_sheet_id,
                        'startRowIndex': start_row,
                        'endRowIndex': start_row + num_rows,
                        'startColumnIndex': start_col,
                        'endColumnIndex': start_col + 4
                    },
                    'top': {'style': 'SOLID', 'width': 1},
                    'bottom': {'style': 'SOLID', 'width': 1},
                    'left': {'style': 'SOLID', 'width': 1},
                    'right': {'style': 'SOLID', 'width': 1},
                    'innerHorizontal': {'style': 'SOLID', 'width': 1},
                    'innerVertical': {'style': 'SOLID', 'width': 1}
                }
            })

            # Thực hiện format
            await self.execute_with_retry(
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': format_requests}
                ),
                "Lỗi khi format bảng tóm tắt"
            )

        except Exception as e:
            self.log(f"❌ Lỗi khi format bảng tóm tắt: {e}")

    async def validate_historical_data_integrity(self, historical_sheet_id):
        """
        Kiểm tra tính toàn vẹn của dữ liệu Historical Data
        """
        try:
            self.log("🔍 Đang kiểm tra tính toàn vẹn dữ liệu Historical Data...")

            # 1. Kiểm tra header structure
            header_response = await self.execute_with_retry(
                self.service.spreadsheets().values().get(
                    spreadsheetId=self.spreadsheet_id,
                    range="'Historical Data'!A1:AA1"
                ),
                "Lỗi khi lấy header"
            )

            if header_response and 'values' in header_response:
                headers = header_response['values'][0] if header_response['values'] else []
                expected_header_count = 27  # 2 + 6 + 1 + 6 + 6 + 6
                actual_header_count = len(headers)

                if actual_header_count != expected_header_count:
                    self.log(f"⚠️ Header count mismatch: Expected {expected_header_count}, got {actual_header_count}")
                else:
                    self.log(f"✅ Header structure OK: {actual_header_count} columns")

            # 2. Kiểm tra data consistency
            data_response = await self.execute_with_retry(
                self.service.spreadsheets().values().get(
                    spreadsheetId=self.spreadsheet_id,
                    range="'Historical Data'!A2:AA29"
                ),
                "Lỗi khi lấy dữ liệu"
            )

            if data_response and 'values' in data_response:
                rows = data_response['values']
                data_rows = [row for row in rows if row and len(row) > 0 and row[0].strip()]

                self.log(f"✅ Tìm thấy {len(data_rows)} dòng dữ liệu")

                # Kiểm tra từng dòng
                for i, row in enumerate(data_rows):
                    row_num = i + 2
                    if len(row) < 15:  # Ít nhất phải có timestamp + time + revenue + separator + percentage
                        self.log(f"⚠️ Dòng {row_num}: Thiếu dữ liệu (chỉ có {len(row)} cột)")

                    # Kiểm tra percentage columns (9-14) có phải là số không
                    for j in range(9, min(15, len(row))):
                        try:
                            float(row[j])
                        except:
                            self.log(f"⚠️ Dòng {row_num}, cột {j+1}: Percentage không phải số: {row[j]}")

            # 3. Kiểm tra biểu đồ
            existing_charts = await self.get_existing_charts(historical_sheet_id)
            chart_count = len(existing_charts)
            self.log(f"✅ Tìm thấy {chart_count} biểu đồ")

            self.log("🎉 Hoàn thành kiểm tra tính toàn vẹn dữ liệu")

        except Exception as e:
            self.log(f"❌ Lỗi khi kiểm tra tính toàn vẹn: {e}")

    async def create_old_pie_charts(self, historical_sheet_id):
        """
        Tạo các pie chart cũ (giữ lại để tham khảo)
        """
        try:
            # Tính toán vị trí biểu đồ - đặt ở vị trí cố định để tránh xung đột
            chart_row_position = 50  # Đặt xa hơn các biểu đồ chính
            print(f"[DEBUG] Old pie chart sẽ được đặt ở dòng {chart_row_position + 1}")

            # Tạo pie chart cho phân bố doanh thu hiện tại (dòng cuối cùng)
            pie_chart_request = {
                'addChart': {
                    'chart': {
                        'spec': {
                            'title': 'AVG CMS từng ngành hàng',
                            'pieChart': {
                                'legendPosition': 'RIGHT_LEGEND',
                                'domain': {
                                    'sourceRange': {
                                        'sources': [
                                            {
                                                'sheetId': historical_sheet_id,
                                                'startRowIndex': 0,  # Header
                                                'endRowIndex': 1,
                                                'startColumnIndex': 2,  # Cột C - tên clusters
                                                'endColumnIndex': 8   # Đến cột H
                                            }
                                        ]
                                    }
                                },
                                'series': {
                                    'sourceRange': {
                                        'sources': [
                                            {
                                                'sheetId': historical_sheet_id,
                                                'startRowIndex': 28,  # Dòng 29 (dòng cuối cùng trong vùng dữ liệu)
                                                'endRowIndex': 29,
                                                'startColumnIndex': 2,  # Cột C - doanh thu clusters
                                                'endColumnIndex': 8   # Đến cột H
                                            }
                                        ]
                                    }
                                }
                            }
                        },
                        'position': {
                            'overlayPosition': {
                                'anchorCell': {
                                    'sheetId': historical_sheet_id,
                                    'rowIndex': chart_row_position,   # Vị trí động
                                    'columnIndex': 40  # Cột AO (tránh xung đột)
                                },
                                'widthPixels': 500,
                                'heightPixels': 400
                            }
                        }
                    }
                }
            }

            # Tạo pie chart thứ hai cho NMV từng ngành hàng
            nmv_pie_chart_request = {
                'addChart': {
                    'chart': {
                        'spec': {
                            'title': 'NMV từng ngành hàng',
                            'pieChart': {
                                'legendPosition': 'RIGHT_LEGEND',
                                'domain': {
                                    'sourceRange': {
                                        'sources': [
                                            {
                                                'sheetId': historical_sheet_id,
                                                'startRowIndex': 0,  # Header
                                                'endRowIndex': 1,
                                                'startColumnIndex': 2,  # Cột C - tên clusters
                                                'endColumnIndex': 8   # Đến cột H
                                            }
                                        ]
                                    }
                                },
                                'series': {
                                    'sourceRange': {
                                        'sources': [
                                            {
                                                'sheetId': historical_sheet_id,
                                                'startRowIndex': 28,  # Dòng 29 (dòng cuối cùng trong vùng dữ liệu)
                                                'endRowIndex': 29,
                                                'startColumnIndex': 2,  # Cột C - doanh thu clusters
                                                'endColumnIndex': 8   # Đến cột H
                                            }
                                        ]
                                    }
                                }
                            }
                        },
                        'position': {
                            'overlayPosition': {
                                'anchorCell': {
                                    'sheetId': historical_sheet_id,
                                    'rowIndex': 25,   # Dòng 26 (dưới pie chart đầu tiên)
                                    'columnIndex': 25  # Cột Z
                                },
                                'widthPixels': 500,
                                'heightPixels': 400
                            }
                        }
                    }
                }
            }

            # Tạo cả hai biểu đồ cùng lúc (giữ lại code cũ để tham khảo)
            # await self.execute_with_retry(
            #     self.service.spreadsheets().batchUpdate(
            #         spreadsheetId=self.spreadsheet_id,
            #         body={'requests': [pie_chart_request, nmv_pie_chart_request]}
            #     ),
            #     "Lỗi khi tạo biểu đồ pie chart timeline"
            # )

            # Tạo bảng Top 15 brand
            await self.create_top_brands_table(historical_sheet_id)

            self.log("✅ Đã hoàn thành tạo các biểu đồ cũ")

        except Exception as e:
            self.log(f"❌ Lỗi khi tạo các biểu đồ cũ: {e}")

    async def create_top_brands_table(self, historical_sheet_id):
        """Tạo bảng Top 15 brand thu CMS income cao"""
        try:
            # Lấy dữ liệu từ TOP GMV sheet để tính toán top brands
            top_gmv_range = "TOP GMV!A3:N502"  # Lấy dữ liệu sản phẩm
            response = await self.execute_with_retry(
                self.service.spreadsheets().values().get(
                    spreadsheetId=self.spreadsheet_id,
                    range=top_gmv_range
                ),
                "Lỗi khi lấy dữ liệu TOP GMV cho top brands"
            )

            if not response or 'values' not in response:
                return

            # Tính toán top brands theo CMS income
            brand_data = {}
            for row in response['values']:
                if len(row) >= 14:  # Đảm bảo có đủ cột
                    try:
                        brand = row[1] if len(row) > 1 else "Unknown"  # Cột B - Tên shop
                        revenue_str = row[7] if len(row) > 7 else "0"  # Cột H - Revenue

                        # Chuyển đổi revenue từ string sang số
                        revenue = 0
                        if revenue_str and revenue_str != "0":
                            # Loại bỏ ký tự ₫, dấu chấm và dấu phẩy
                            clean_revenue = revenue_str.replace("₫", "").replace(".", "").replace(",", "")
                            revenue = float(clean_revenue) if clean_revenue.isdigit() else 0

                        if brand in brand_data:
                            brand_data[brand] += revenue
                        else:
                            brand_data[brand] = revenue
                    except:
                        continue

            # Sắp xếp và lấy top 15
            sorted_brands = sorted(brand_data.items(), key=lambda x: x[1], reverse=True)[:15]
            total_revenue = sum(brand_data.values())

            # Tạo dữ liệu cho bảng
            table_data = [
                ["Top 15 brand thu về CMS income cao", "", ""],
                ["Tên Shop", "Sum of Giá trị mua hàng(₫)", "Tỷ lệ hoạ đơn người bán"],
                ["", "", ""]
            ]

            for i, (brand, revenue) in enumerate(sorted_brands, 1):
                percentage = (revenue / total_revenue * 100) if total_revenue > 0 else 0
                formatted_revenue = f"{revenue:,.0f}".replace(",", ".")
                table_data.append([
                    brand,
                    formatted_revenue,
                    f"{percentage:.0f}%"
                ])

            # Thêm dòng Total
            total_formatted = f"{total_revenue:,.0f}".replace(",", ".")
            table_data.append(["Total", total_formatted, "100%"])

            # Ghi dữ liệu vào sheet
            table_range = f"'Historical Data'!A{len(response.get('values', [])) + 10}:C{len(response.get('values', [])) + 10 + len(table_data)}"

            await self.execute_with_retry(
                self.service.spreadsheets().values().update(
                    spreadsheetId=self.spreadsheet_id,
                    range=table_range,
                    valueInputOption='USER_ENTERED',
                    body={'values': table_data}
                ),
                "Lỗi khi ghi bảng top brands"
            )

            self.log("✅ Đã tạo bảng Top 15 brands")

        except Exception as e:
            self.log(f"❌ Lỗi khi tạo bảng top brands: {e}")

    async def create_historical_dashboard(self, historical_sheet_id, clusters_data):
        """
        Tạo dashboard phân tích trong sheet Historical Data
        """
        try:
            self.log("Đang tạo dashboard phân tích trong sheet Historical Data...")

            # Tạo tiêu đề dashboard
            title_request = {
                'updateCells': {
                    'range': {
                        'sheetId': historical_sheet_id,
                        'startRowIndex': 0,
                        'endRowIndex': 2,
                        'startColumnIndex': 0,
                        'endColumnIndex': 10
                    },
                    'rows': [
                        {
                            'values': [
                                {'userEnteredValue': {'stringValue': 'CLUSTER ANALYSIS DASHBOARD'}},
                                {'userEnteredValue': {'stringValue': ''}},
                                {'userEnteredValue': {'stringValue': ''}},
                                {'userEnteredValue': {'stringValue': ''}},
                                {'userEnteredValue': {'stringValue': ''}},
                                {'userEnteredValue': {'stringValue': ''}},
                                {'userEnteredValue': {'stringValue': f'Updated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'}},
                            ]
                        },
                        {
                            'values': [
                                {'userEnteredValue': {'stringValue': ''}},
                                {'userEnteredValue': {'stringValue': ''}},
                                {'userEnteredValue': {'stringValue': ''}},
                                {'userEnteredValue': {'stringValue': ''}},
                                {'userEnteredValue': {'stringValue': ''}},
                                {'userEnteredValue': {'stringValue': ''}},
                                {'userEnteredValue': {'stringValue': ''}},
                            ]
                        }
                    ],
                    'fields': 'userEnteredValue'
                }
            }

            await self.execute_with_retry(
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': [title_request]}
                ),
                "Lỗi khi tạo tiêu đề dashboard"
            )

            # Tạo bảng tổng hợp cluster
            await self.create_cluster_summary_in_historical(historical_sheet_id, clusters_data)

            # Tạo biểu đồ
            await self.create_charts_in_historical(historical_sheet_id)

            self.log("✅ Đã tạo dashboard phân tích trong Historical Data")

        except Exception as e:
            self.log(f"❌ Lỗi khi tạo dashboard: {e}")

    async def create_cluster_summary_in_historical(self, historical_sheet_id, clusters_data):
        """
        Tạo bảng tổng hợp cluster trong Historical Data sheet
        """
        try:
            # Tạo header cho bảng tổng hợp
            header_request = {
                'updateCells': {
                    'range': {
                        'sheetId': historical_sheet_id,
                        'startRowIndex': 3,  # Dòng 4
                        'endRowIndex': 5,    # Đến dòng 5
                        'startColumnIndex': 0,
                        'endColumnIndex': 4
                    },
                    'rows': [
                        {
                            'values': [
                                {'userEnteredValue': {'stringValue': 'Cluster'}},
                                {'userEnteredValue': {'stringValue': 'SKU Count'}},
                                {'userEnteredValue': {'stringValue': 'Revenue'}},
                                {'userEnteredValue': {'stringValue': 'Percentage'}}
                            ]
                        }
                    ],
                    'fields': 'userEnteredValue'
                }
            }

            await self.execute_with_retry(
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': [header_request]}
                ),
                "Lỗi khi tạo header bảng tổng hợp"
            )

            # Tạo dữ liệu cho bảng tổng hợp
            summary_data = []
            for cluster_info in clusters_data:
                cluster_name = cluster_info[0]
                sku_count = cluster_info[1]  # SKU Sold formula
                revenue = cluster_info[2]    # Revenue formula
                percentage = cluster_info[3] if len(cluster_info) > 3 else ""  # Percentage formula

                summary_data.append([cluster_name, sku_count, revenue, percentage])

            # Cập nhật dữ liệu
            if summary_data:
                data_range = f"'Historical Data'!A5:D{4 + len(summary_data)}"
                await self.execute_with_retry(
                    self.service.spreadsheets().values().update(
                        spreadsheetId=self.spreadsheet_id,
                        range=data_range,
                        valueInputOption="USER_ENTERED",
                        body={"values": summary_data}
                    ),
                    "Lỗi khi cập nhật dữ liệu bảng tổng hợp"
                )

            # Định dạng bảng
            format_request = {
                'repeatCell': {
                    'range': {
                        'sheetId': historical_sheet_id,
                        'startRowIndex': 3,
                        'endRowIndex': 4,
                        'startColumnIndex': 0,
                        'endColumnIndex': 4
                    },
                    'cell': {
                        'userEnteredFormat': {
                            'backgroundColor': {'red': 0.2, 'green': 0.4, 'blue': 0.8},
                            'horizontalAlignment': 'CENTER',
                            'textFormat': {'bold': True, 'foregroundColor': {'red': 1, 'green': 1, 'blue': 1}}
                        }
                    },
                    'fields': 'userEnteredFormat(backgroundColor,textFormat,horizontalAlignment)'
                }
            }

            await self.execute_with_retry(
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': [format_request]}
                ),
                "Lỗi khi định dạng bảng tổng hợp"
            )

        except Exception as e:
            self.log(f"❌ Lỗi khi tạo bảng tổng hợp: {e}")

    async def create_charts_in_historical(self, historical_sheet_id):
        """
        Tạo biểu đồ trong Historical Data sheet
        """
        try:
            # Tạo biểu đồ cột cho doanh thu
            column_chart_request = {
                'addChart': {
                    'chart': {
                        'spec': {
                            'title': 'Revenue by Cluster',
                            'basicChart': {
                                'chartType': 'COLUMN',
                                'legendPosition': 'BOTTOM_LEGEND',
                                'axis': [
                                    {
                                        'position': 'BOTTOM_AXIS',
                                        'title': 'Cluster'
                                    },
                                    {
                                        'position': 'LEFT_AXIS',
                                        'title': 'Revenue'
                                    }
                                ],
                                'domains': [
                                    {
                                        'domain': {
                                            'sourceRange': {
                                                'sources': [
                                                    {
                                                        'sheetId': historical_sheet_id,
                                                        'startRowIndex': 4,  # Dòng 5
                                                        'endRowIndex': 10,   # 6 clusters
                                                        'startColumnIndex': 0, # Cột A
                                                        'endColumnIndex': 1
                                                    }
                                                ]
                                            }
                                        }
                                    }
                                ],
                                'series': [
                                    {
                                        'series': {
                                            'sourceRange': {
                                                'sources': [
                                                    {
                                                        'sheetId': historical_sheet_id,
                                                        'startRowIndex': 4,  # Dòng 5
                                                        'endRowIndex': 10,   # 6 clusters
                                                        'startColumnIndex': 2, # Cột C (Revenue)
                                                        'endColumnIndex': 3
                                                    }
                                                ]
                                            }
                                        },
                                        'targetAxis': 'LEFT_AXIS'
                                    }
                                ]
                            }
                        },
                        'position': {
                            'overlayPosition': {
                                'anchorCell': {
                                    'sheetId': historical_sheet_id,
                                    'rowIndex': 12,  # Dòng 13
                                    'columnIndex': 0  # Cột A
                                },
                                'widthPixels': 600,
                                'heightPixels': 400
                            }
                        }
                    }
                }
            }

            # Tạo biểu đồ tròn cho tỷ lệ phần trăm
            pie_chart_request = {
                'addChart': {
                    'chart': {
                        'spec': {
                            'title': 'Revenue Distribution by Cluster',
                            'pieChart': {
                                'legendPosition': 'RIGHT_LEGEND',
                                'domain': {
                                    'sourceRange': {
                                        'sources': [
                                            {
                                                'sheetId': historical_sheet_id,
                                                'startRowIndex': 4,  # Dòng 5
                                                'endRowIndex': 10,   # 6 clusters
                                                'startColumnIndex': 0, # Cột A
                                                'endColumnIndex': 1
                                            }
                                        ]
                                    }
                                },
                                'series': {
                                    'sourceRange': {
                                        'sources': [
                                            {
                                                'sheetId': historical_sheet_id,
                                                'startRowIndex': 4,  # Dòng 5
                                                'endRowIndex': 10,   # 6 clusters
                                                'startColumnIndex': 3, # Cột D (Percentage)
                                                'endColumnIndex': 4
                                            }
                                        ]
                                    }
                                }
                            }
                        },
                        'position': {
                            'overlayPosition': {
                                'anchorCell': {
                                    'sheetId': historical_sheet_id,
                                    'rowIndex': 12,  # Dòng 13
                                    'columnIndex': 8   # Cột I
                                },
                                'widthPixels': 500,
                                'heightPixels': 400
                            }
                        }
                    }
                }
            }

            # Gửi request để tạo biểu đồ
            await self.execute_with_retry(
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': [column_chart_request, pie_chart_request]}
                ),
                "Lỗi khi tạo biểu đồ"
            )

            self.log("✅ Đã tạo biểu đồ phân tích thành công")

        except Exception as e:
            self.log(f"❌ Lỗi khi tạo biểu đồ: {e}")

    async def create_pivot_table(self, top_gmv_sheet_id):
        """
        Tạo Pivot Table để phân tích dữ liệu cluster
        """
        try:
            self.log("Đang tạo Pivot Table cho phân tích cluster...")

            # Tạo Pivot Table request
            pivot_table_request = {
                'updateCells': {
                    'range': {
                        'sheetId': top_gmv_sheet_id,
                        'startRowIndex': 12,  # Bắt đầu từ dòng 13
                        'endRowIndex': 20,
                        'startColumnIndex': 10,  # Cột K
                        'endColumnIndex': 14     # Đến cột N
                    },
                    'rows': [
                        {
                            'values': [
                                {'userEnteredValue': {'stringValue': 'CLUSTER ANALYSIS'}},
                                {'userEnteredValue': {'stringValue': ''}},
                                {'userEnteredValue': {'stringValue': ''}},
                                {'userEnteredValue': {'stringValue': ''}}
                            ]
                        },
                        {
                            'values': [
                                {'userEnteredValue': {'stringValue': 'Cluster'}},
                                {'userEnteredValue': {'stringValue': 'SKU Count'}},
                                {'userEnteredValue': {'stringValue': 'Revenue'}},
                                {'userEnteredValue': {'stringValue': 'Percentage'}}
                            ]
                        }
                    ],
                    'fields': 'userEnteredValue'
                }
            }

            # Gửi request để tạo header
            await self.execute_with_retry(
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': [pivot_table_request]}
                ),
                "Lỗi khi tạo Pivot Table header"
            )

            # Định dạng header
            format_request = {
                'repeatCell': {
                    'range': {
                        'sheetId': top_gmv_sheet_id,
                        'startRowIndex': 12,
                        'endRowIndex': 14,
                        'startColumnIndex': 10,
                        'endColumnIndex': 14
                    },
                    'cell': {
                        'userEnteredFormat': {
                            'backgroundColor': {'red': 0.2, 'green': 0.4, 'blue': 0.8},
                            'horizontalAlignment': 'CENTER',
                            'textFormat': {'bold': True, 'foregroundColor': {'red': 1, 'green': 1, 'blue': 1}}
                        }
                    },
                    'fields': 'userEnteredFormat(backgroundColor,textFormat,horizontalAlignment)'
                }
            }

            await self.execute_with_retry(
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': [format_request]}
                ),
                "Lỗi khi định dạng Pivot Table"
            )

            self.log("✅ Đã tạo Pivot Table thành công")

        except Exception as e:
            self.log(f"❌ Lỗi khi tạo Pivot Table: {e}")

    async def create_trend_charts(self):
        """
        Tạo Pivot Table và biểu đồ cho các Cluster
        """
        try:
            # Lấy sheet ID của sheet TOP GMV
            spreadsheet = await self.execute_with_retry(
                self.service.spreadsheets().get(spreadsheetId=self.spreadsheet_id),
                "Lỗi khi lấy thông tin sheet"
            )

            top_gmv_sheet_id = None

            for sheet in spreadsheet.get('sheets', []):
                if sheet['properties']['title'] == 'TOP GMV':
                    top_gmv_sheet_id = sheet['properties']['sheetId']
                    break

            if not top_gmv_sheet_id:
                self.log("❌ Không tìm thấy sheet TOP GMV để tạo biểu đồ")
                return

            # Tạo Pivot Table trước
            await self.create_pivot_table(top_gmv_sheet_id)

            # Tạo biểu đồ cột (Column Chart) cho doanh thu cluster
            revenue_chart_request = {
                'addChart': {
                    'chart': {
                        'spec': {
                            'title': 'Cluster Revenue Analysis',
                            'basicChart': {
                                'chartType': 'COLUMN',
                                'legendPosition': 'BOTTOM_LEGEND',
                                'axis': [
                                    {
                                        'position': 'BOTTOM_AXIS',
                                        'title': 'Cluster'
                                    },
                                    {
                                        'position': 'LEFT_AXIS',
                                        'title': 'Revenue'
                                    }
                                ],
                                'domains': [
                                    {
                                        'domain': {
                                            'sourceRange': {
                                                'sources': [
                                                    {
                                                        'sheetId': top_gmv_sheet_id,
                                                        'startRowIndex': 2,  # Dòng 3 (K3)
                                                        'endRowIndex': 8,    # Đến dòng 8 (6 clusters)
                                                        'startColumnIndex': 10, # Cột K (Cluster names)
                                                        'endColumnIndex': 11
                                                    }
                                                ]
                                            }
                                        }
                                    }
                                ],
                                'series': [
                                    {
                                        'series': {
                                            'sourceRange': {
                                                'sources': [
                                                    {
                                                        'sheetId': top_gmv_sheet_id,
                                                        'startRowIndex': 2,  # Dòng 3 (M3)
                                                        'endRowIndex': 8,    # Đến dòng 8
                                                        'startColumnIndex': 12, # Cột M (Revenue)
                                                        'endColumnIndex': 13
                                                    }
                                                ]
                                            }
                                        },
                                        'targetAxis': 'LEFT_AXIS'
                                    }
                                ]
                            }
                        },
                        'position': {
                            'overlayPosition': {
                                'anchorCell': {
                                    'sheetId': top_gmv_sheet_id,
                                    'rowIndex': 15,  # Đặt dưới Pivot Table
                                    'columnIndex': 10
                                },
                                'widthPixels': 600,
                                'heightPixels': 350
                            }
                        }
                    }
                }
            }

            # Tạo biểu đồ tròn (Pie Chart) cho tỷ lệ phần trăm
            percentage_chart_request = {
                'addChart': {
                    'chart': {
                        'spec': {
                            'title': 'Cluster Revenue Distribution',
                            'pieChart': {
                                'legendPosition': 'RIGHT_LEGEND',
                                'domain': {
                                    'sourceRange': {
                                        'sources': [
                                            {
                                                'sheetId': top_gmv_sheet_id,
                                                'startRowIndex': 2,  # Dòng 3 (K3)
                                                'endRowIndex': 8,    # Đến dòng 8 (6 clusters)
                                                'startColumnIndex': 10, # Cột K (Cluster names)
                                                'endColumnIndex': 11
                                            }
                                        ]
                                    }
                                },
                                'series': {
                                    'sourceRange': {
                                        'sources': [
                                            {
                                                'sheetId': top_gmv_sheet_id,
                                                'startRowIndex': 2,  # Dòng 3 (N3)
                                                'endRowIndex': 8,    # Đến dòng 8
                                                'startColumnIndex': 13, # Cột N (Percentage)
                                                'endColumnIndex': 14
                                            }
                                        ]
                                    }
                                }
                            }
                        },
                        'position': {
                            'overlayPosition': {
                                'anchorCell': {
                                    'sheetId': top_gmv_sheet_id,
                                    'rowIndex': 15,  # Cùng hàng với biểu đồ cột
                                    'columnIndex': 17  # Cột R (bên phải biểu đồ cột)
                                },
                                'widthPixels': 500,
                                'heightPixels': 350
                            }
                        }
                    }
                }
            }

            # Gửi request để tạo biểu đồ
            await self.execute_with_retry(
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': [revenue_chart_request, percentage_chart_request]}
                ),
                "Lỗi khi tạo biểu đồ"
            )

            self.log("✅ Đã tạo biểu đồ xu hướng Cluster thành công")

        except Exception as e:
            self.log(f"❌ Lỗi khi tạo biểu đồ xu hướng: {e}")
            import traceback
            self.log(traceback.format_exc())

    def show_recalculate_dialog(self):
        """Hiển thị dialog để chọn timestamp muốn tính toán lại"""
        dialog = QDialog(self)
        dialog.setWindowTitle("Tính toán lại dữ liệu")
        dialog.setMinimumWidth(400)

        layout = QVBoxLayout(dialog)

        # Label hướng dẫn
        label = QLabel("Chọn timestamp muốn tính toán lại:")
        layout.addWidget(label)

        # ComboBox chọn timestamp
        timestamp_combo = QComboBox()
        timestamp_combo.setMinimumWidth(350)

        # Nút xử lý
        recalc_button = QPushButton("Tính toán lại")
        cancel_button = QPushButton("Hủy")

        # Layout cho các nút
        button_layout = QHBoxLayout()
        button_layout.addWidget(recalc_button)
        button_layout.addWidget(cancel_button)

        layout.addWidget(timestamp_combo)
        layout.addLayout(button_layout)

        # Lấy danh sách timestamps từ Historical Data
        def load_timestamps():
            self.log("Đang tải danh sách timestamps...")
            timestamp_combo.clear()
            timestamp_combo.addItem("Tất cả timestamps")

            threading.Thread(target=lambda: asyncio.run(fetch_timestamps()), daemon=True).start()

        async def fetch_timestamps():
            try:
                spreadsheet_id = self.parse_spreadsheet_id(self.url_input.text().strip())
                if not spreadsheet_id:
                    self.log("❌ Không có Spreadsheet ID hợp lệ")
                    return

                response = await self.execute_with_retry(
                    self.service.spreadsheets().values().get(
                        spreadsheetId=spreadsheet_id,
                        range="'Historical Data'!A2:A1000"
                    ),
                    "Lỗi khi lấy danh sách timestamps"
                )

                if response and 'values' in response:
                    timestamps = [row[0] for row in response['values']]

                    def update_combo():
                        for ts in timestamps:
                            timestamp_combo.addItem(ts)
                        self.log(f"✅ Đã tải {len(timestamps)} timestamps")

                    # Cập nhật UI từ main thread
                    QTimer.singleShot(0, update_combo)
            except Exception as e:
                self.log(f"❌ Lỗi khi tải timestamps: {e}")

        # Kết nối các sự kiện
        recalc_button.clicked.connect(lambda: start_recalculation(timestamp_combo.currentText()))
        cancel_button.clicked.connect(dialog.close)

        # Bắt đầu tính toán lại
        def start_recalculation(selected_timestamp):
            self.log(f"Bắt đầu tính toán lại cho timestamp: {selected_timestamp}")
            dialog.close()

            # Chạy trong thread riêng để không block UI
            threading.Thread(
                target=lambda: asyncio.run(self.recalculate_historical_data(selected_timestamp)),
                daemon=True
            ).start()

        # Tải danh sách timestamps khi mở dialog
        load_timestamps()

        # Hiển thị dialog
        dialog.exec()

    async def recalculate_historical_data(self, selected_timestamp):
        """Tính toán lại dữ liệu lịch sử dựa trên Cluster hiện tại"""
        try:
            self.log(f"Đang tính toán lại dữ liệu cho: {selected_timestamp}")
            spreadsheet_id = self.parse_spreadsheet_id(self.url_input.text().strip())

            if not spreadsheet_id:
                self.log("❌ Không có Spreadsheet ID hợp lệ")
                return

            # Kiểm tra xem sheet Historical Data đã tồn tại chưa
            sheet_metadata = await self.execute_with_retry(
                self.service.spreadsheets().get(spreadsheetId=spreadsheet_id),
                "Lỗi khi kiểm tra sheet Historical Data"
            )

            if not sheet_metadata:
                self.log("❌ Không thể lấy thông tin sheet")
                return

            sheet_exists = False
            for sheet in sheet_metadata.get('sheets', []):
                if sheet['properties']['title'] == 'Historical Data':
                    sheet_exists = True
                    break

            if not sheet_exists:
                self.log("❌ Không tìm thấy sheet Historical Data")
                return

            # Lấy dữ liệu từ Historical Data
            if selected_timestamp == "Tất cả timestamps":
                # Lấy toàn bộ dữ liệu
                response = await self.execute_with_retry(
                    self.service.spreadsheets().values().get(
                        spreadsheetId=spreadsheet_id,
                        range="'Historical Data'!A2:A1000"
                    ),
                    "Lỗi khi lấy dữ liệu timestamp"
                )

                if response and 'values' in response:
                    timestamps = [row[0] for row in response['values']]
                    self.log(f"Tìm thấy {len(timestamps)} timestamps cần tính toán lại")

                    for ts in timestamps:
                        await self.recalculate_single_timestamp(spreadsheet_id, ts)
                else:
                    self.log("❌ Không tìm thấy dữ liệu timestamp")
            else:
                # Chỉ tính toán lại cho timestamp đã chọn
                await self.recalculate_single_timestamp(spreadsheet_id, selected_timestamp)

            self.log("✅ Đã hoàn tất tính toán lại dữ liệu lịch sử")

        except Exception as e:
            self.log(f"❌ Lỗi khi tính toán lại dữ liệu: {e}")
            import traceback
            self.log(traceback.format_exc())

    async def recalculate_single_timestamp(self, spreadsheet_id, timestamp):
        """Tính toán lại dữ liệu cho một timestamp cụ thể"""
        try:
            # Tìm dòng của timestamp trong Historical Data
            response = await self.execute_with_retry(
                self.service.spreadsheets().values().get(
                    spreadsheetId=spreadsheet_id,
                    range="'Historical Data'!A2:A1000"
                ),
                f"Lỗi khi tìm dòng cho timestamp {timestamp}"
            )

            if not response or 'values' not in response:
                self.log(f"❌ Không thể tìm dòng cho timestamp {timestamp}")
                return

            row_idx = None
            for i, row in enumerate(response.get('values', [])):
                if row and row[0] == timestamp:
                    row_idx = i + 2  # +2 vì bắt đầu từ A2
                    break

            if row_idx is None:
                self.log(f"❌ Không tìm thấy timestamp {timestamp} trong dữ liệu")
                return

            self.log(f"Đang xử lý timestamp {timestamp} tại dòng {row_idx}")

            sheet = self.sheet_selector.currentText()

            # Lấy dữ liệu Cluster hiện tại từ sheet chính
            cluster_data = await self.execute_with_retry(
                self.service.spreadsheets().values().get(
                    spreadsheetId=spreadsheet_id,
                    range=f"{sheet}!D3:I1000"  # Lấy Item ID (D) và Cluster (I)
                ),
                f"Lỗi khi lấy dữ liệu Cluster cho timestamp {timestamp}"
            )

            if not cluster_data or 'values' not in cluster_data:
                self.log(f"❌ Không thể lấy dữ liệu Cluster hiện tại")
                return

            # Tạo map từ Item ID sang Cluster
            item_to_cluster = {}
            for row in cluster_data.get('values', []):
                if len(row) >= 6:  # Đủ cột để lấy Item ID và Cluster
                    item_id = row[0]  # Item ID (cột D)
                    cluster = row[5] if len(row) > 5 else ""  # Cluster (cột I)
                    if item_id and cluster:
                        item_to_cluster[item_id] = cluster

            # Lấy doanh thu từ sheet chính
            revenue_data = await self.execute_with_retry(
                self.service.spreadsheets().values().get(
                    spreadsheetId=spreadsheet_id,
                    range=f"{sheet}!D3:H1000"  # Lấy Item ID (D) và Doanh thu (H)
                ),
                f"Lỗi khi lấy dữ liệu doanh thu cho timestamp {timestamp}"
            )

            if not revenue_data or 'values' not in revenue_data:
                self.log(f"❌ Không thể lấy dữ liệu doanh thu hiện tại")
                return

            # Tạo map từ Item ID sang doanh thu
            item_to_revenue = {}
            for row in revenue_data.get('values', []):
                if len(row) >= 5:  # Đủ cột để lấy Item ID và Doanh thu
                    item_id = row[0]  # Item ID (cột D)
                    revenue = row[4] if len(row) > 4 else "₫ 0"  # Doanh thu (cột H)
                    if item_id:
                        item_to_revenue[item_id] = revenue

            # Tính toán lại dữ liệu cho từng Cluster
            cluster_revenues = {cluster: 0 for cluster in CLUSTERS}

            # Tính tổng doanh thu cho từng Cluster
            for item_id, cluster in item_to_cluster.items():
                if item_id in item_to_revenue:
                    revenue_str = item_to_revenue[item_id]
                    try:
                        # Chuyển chuỗi doanh thu thành số
                        revenue_value = float(revenue_str.replace("₫", "").replace(".", "").replace(",", ".").strip())

                        # Nếu Cluster nằm trong danh sách cần tính
                        if cluster in CLUSTERS:
                            cluster_revenues[cluster] += revenue_value
                    except Exception as e:
                        self.log(f"⚠️ Lỗi khi xử lý doanh thu cho item {item_id}: {e}")

            # Tính tổng doanh thu
            total_revenue = sum(cluster_revenues.values())

            # Chuẩn bị dữ liệu cập nhật cho Historical Data
            updated_data = []

            # Thêm doanh thu của từng Cluster (cột B-G)
            for cluster in CLUSTERS:
                updated_data.append(str(cluster_revenues[cluster]))

            # Thêm tỷ lệ % của từng Cluster (cột H-M)
            for cluster in CLUSTERS:
                percentage = (cluster_revenues[cluster] / total_revenue) if total_revenue > 0 else 0
                updated_data.append(str(percentage))

            # Cập nhật dữ liệu vào sheet Historical Data (không cập nhật cột timestamp)
            await self.execute_with_retry(
                self.service.spreadsheets().values().update(
                    spreadsheetId=spreadsheet_id,
                    range=f"'Historical Data'!B{row_idx}:M{row_idx}",
                    valueInputOption="USER_ENTERED",
                    body={"values": [updated_data]}
                ),
                f"Lỗi khi cập nhật dữ liệu cho timestamp {timestamp}"
            )

            self.log(f"✅ Đã tính toán lại dữ liệu cho timestamp {timestamp}")

        except Exception as e:
            self.log(f"❌ Lỗi khi tính toán lại cho timestamp {timestamp}: {e}")
            import traceback
            self.log(traceback.format_exc())

    def show_add_account_dialog(self):
        dialog = QWidget()
        dialog.setWindowTitle("Thêm tài khoản mới")
        layout = QVBoxLayout(dialog)
        user_input = QLineEdit()
        user_input.setPlaceholderText("Username")
        pass_input = QLineEdit()
        pass_input.setPlaceholderText("Password")
        pass_input.setEchoMode(QLineEdit.EchoMode.Password)
        label_input = QLineEdit()
        label_input.setPlaceholderText("Label")
        save_btn = QPushButton("Lưu")
        cancel_btn = QPushButton("Hủy")
        btn_row = QHBoxLayout()
        btn_row.addWidget(save_btn)
        btn_row.addWidget(cancel_btn)
        layout.addWidget(QLabel("Username:"))
        layout.addWidget(user_input)
        layout.addWidget(QLabel("Password:"))
        layout.addWidget(pass_input)
        layout.addWidget(QLabel("Label hiển thị:"))
        layout.addWidget(label_input)
        layout.addLayout(btn_row)
        dialog.setLayout(layout)
        def save():
            username = user_input.text().strip()
            password = pass_input.text().strip()
            label = label_input.text().strip()
            if not username or not password or not label:
                QMessageBox.warning(dialog, "Thiếu thông tin", "Vui lòng nhập đủ thông tin.")
                return
            new_acc = {"username": username, "password": password, "label": label}
            self.accounts.append(new_acc)
            with open(ACCOUNT_FILE, "w", encoding="utf-8") as f:
                json.dump(self.accounts, f, ensure_ascii=False, indent=2)
            self.account_selector.addItem(label)

            # Kích hoạt nút "Thực hiện" nếu đây là tài khoản đầu tiên
            if len(self.accounts) == 1:
                self.update_start_button_state()

            dialog.close()
        save_btn.clicked.connect(save)
        cancel_btn.clicked.connect(dialog.close)
        dialog.setWindowModality(Qt.WindowModality.ApplicationModal)
        dialog.setFixedSize(300, 200)
        dialog.show()

    def show_edit_account_dialog(self):
        # Kiểm tra xem có tài khoản nào được chọn không
        current_index = self.account_selector.currentIndex()
        if current_index < 0 or current_index >= len(self.accounts):
            QMessageBox.warning(self, "Lỗi", "Vui lòng chọn tài khoản cần sửa.")
            return

        # Lấy thông tin tài khoản hiện tại
        current_account = self.accounts[current_index]

        # Tạo dialog sửa tài khoản
        dialog = QWidget()
        dialog.setWindowTitle("Sửa tài khoản")
        layout = QVBoxLayout(dialog)

        # Tạo các trường nhập liệu
        user_input = QLineEdit()
        user_input.setText(current_account["username"])
        user_input.setPlaceholderText("Username")

        pass_input = QLineEdit()
        pass_input.setText(current_account["password"])
        pass_input.setPlaceholderText("Password")
        pass_input.setEchoMode(QLineEdit.EchoMode.Password)

        label_input = QLineEdit()
        label_input.setText(current_account["label"])
        label_input.setPlaceholderText("Label")

        # Tạo các nút
        save_btn = QPushButton("Lưu")
        cancel_btn = QPushButton("Hủy")
        btn_row = QHBoxLayout()
        btn_row.addWidget(save_btn)
        btn_row.addWidget(cancel_btn)

        # Thêm các thành phần vào layout
        layout.addWidget(QLabel("Username:"))
        layout.addWidget(user_input)
        layout.addWidget(QLabel("Password:"))
        layout.addWidget(pass_input)
        layout.addWidget(QLabel("Label hiển thị:"))
        layout.addWidget(label_input)
        layout.addLayout(btn_row)
        dialog.setLayout(layout)

        # Hàm lưu thay đổi
        def save():
            username = user_input.text().strip()
            password = pass_input.text().strip()
            label = label_input.text().strip()

            if not username or not password or not label:
                QMessageBox.warning(dialog, "Thiếu thông tin", "Vui lòng nhập đủ thông tin.")
                return

            # Cập nhật thông tin tài khoản
            self.accounts[current_index] = {"username": username, "password": password, "label": label}

            # Lưu vào file
            with open(ACCOUNT_FILE, "w", encoding="utf-8") as f:
                json.dump(self.accounts, f, ensure_ascii=False, indent=2)

            # Cập nhật combobox
            self.account_selector.setItemText(current_index, label)

            # Thông báo thành công
            self.log(f"Đã cập nhật thông tin tài khoản: {label}")

            # Đóng dialog
            dialog.close()

        # Kết nối các sự kiện
        save_btn.clicked.connect(save)
        cancel_btn.clicked.connect(dialog.close)

        # Hiển thị dialog
        dialog.setWindowModality(Qt.WindowModality.ApplicationModal)
        dialog.setFixedSize(300, 200)
        dialog.show()

    def delete_account(self):
        # Kiểm tra xem có tài khoản nào được chọn không
        current_index = self.account_selector.currentIndex()
        if current_index < 0 or current_index >= len(self.accounts):
            QMessageBox.warning(self, "Lỗi", "Vui lòng chọn tài khoản cần xóa.")
            return

        # Lấy thông tin tài khoản hiện tại
        current_account = self.accounts[current_index]

        # Hiển thị hộp thoại xác nhận
        confirm = QMessageBox.question(
            self,
            "Xác nhận xóa",
            f"Bạn có chắc chắn muốn xóa tài khoản '{current_account['label']}'?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if confirm == QMessageBox.StandardButton.Yes:
            # Xóa tài khoản khỏi danh sách
            del self.accounts[current_index]

            # Lưu vào file
            with open(ACCOUNT_FILE, "w", encoding="utf-8") as f:
                json.dump(self.accounts, f, ensure_ascii=False, indent=2)

            # Cập nhật combobox
            self.account_selector.removeItem(current_index)

            # Thông báo thành công
            self.log(f"Đã xóa tài khoản: {current_account['label']}")

            # Kiểm tra nếu không còn tài khoản nào
            if len(self.accounts) == 0:
                self.update_start_button_state()
                self.log("Không còn tài khoản nào. Vui lòng thêm tài khoản trước khi tiếp tục.")

    def import_cookie_from_browser(self):
        """Import cookie từ trình duyệt khác"""
        dialog = QDialog(self)
        dialog.setWindowTitle("Import Cookie từ Browser")
        dialog.setFixedSize(500, 400)

        layout = QVBoxLayout(dialog)

        # Hướng dẫn
        instruction_label = QLabel("""
<b>Hướng dẫn:</b><br>
1. Mở trình duyệt và đăng nhập vào Shopee Creator<br>
2. Mở Developer Tools (F12)<br>
3. Vào tab Application/Storage > Cookies > https://creator.shopee.vn<br>
4. Copy các cookie quan trọng và nhập vào đây<br><br>
<b>Hoặc chọn file JSON đã export từ trình duyệt</b>
        """)
        instruction_label.setWordWrap(True)
        layout.addWidget(instruction_label)

        # Tab widget để chọn phương thức
        tab_widget = QTabWidget()

        # Tab 1: Nhập cookie thủ công
        manual_tab = QWidget()
        manual_layout = QVBoxLayout(manual_tab)

        # Danh sách cookie quan trọng
        important_cookies = [
            "SPC_F", "SPC_R_T_ID", "SPC_R_T_IV", "SPC_SI", "SPC_ST",
            "SPC_T_ID", "SPC_T_IV", "shopee_token", "userid", "username"
        ]

        cookie_inputs = {}
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        for cookie_name in important_cookies:
            cookie_layout = QHBoxLayout()
            label = QLabel(f"{cookie_name}:")
            label.setFixedWidth(120)
            input_field = QLineEdit()
            input_field.setPlaceholderText("Nhập giá trị cookie (bỏ trống nếu không có)")
            cookie_inputs[cookie_name] = input_field
            cookie_layout.addWidget(label)
            cookie_layout.addWidget(input_field)
            scroll_layout.addLayout(cookie_layout)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        manual_layout.addWidget(scroll_area)

        tab_widget.addTab(manual_tab, "Nhập thủ công")

        # Tab 2: Import từ file
        file_tab = QWidget()
        file_layout = QVBoxLayout(file_tab)

        file_info_label = QLabel("Chọn file JSON chứa cookie đã export từ trình duyệt:")
        file_layout.addWidget(file_info_label)

        file_path_input = QLineEdit()
        file_path_input.setPlaceholderText("Đường dẫn file JSON...")
        file_browse_button = QPushButton("Chọn file")

        def browse_file():
            file_path, _ = QFileDialog.getOpenFileName(
                dialog, "Chọn file cookie", "", "JSON files (*.json);;Text files (*.txt);;All files (*.*)"
            )
            if file_path:
                file_path_input.setText(file_path)

        file_browse_button.clicked.connect(browse_file)

        file_row = QHBoxLayout()
        file_row.addWidget(file_path_input)
        file_row.addWidget(file_browse_button)
        file_layout.addLayout(file_row)

        tab_widget.addTab(file_tab, "Import từ file")

        layout.addWidget(tab_widget)

        # Buttons
        button_layout = QHBoxLayout()
        create_button = QPushButton("Tạo Session")
        cancel_button = QPushButton("Hủy")

        def create_session():
            try:
                current_tab = tab_widget.currentIndex()
                cookies = []

                if current_tab == 0:  # Manual input
                    # Thu thập cookie từ input fields
                    for cookie_name, input_field in cookie_inputs.items():
                        value = input_field.text().strip()
                        if value:
                            cookies.append({
                                "name": cookie_name,
                                "value": value,
                                "domain": ".shopee.vn",
                                "path": "/",
                                "httpOnly": True,
                                "secure": True
                            })

                    if not cookies:
                        QMessageBox.warning(dialog, "Lỗi", "Vui lòng nhập ít nhất một cookie!")
                        return

                else:  # File import
                    file_path = file_path_input.text().strip()
                    if not file_path or not os.path.exists(file_path):
                        QMessageBox.warning(dialog, "Lỗi", "Vui lòng chọn file hợp lệ!")
                        return

                    cookies = self.parse_cookie_file(file_path)
                    if not cookies:
                        QMessageBox.warning(dialog, "Lỗi", "Không thể đọc cookie từ file!")
                        return

                # Tạo storage state
                storage_state = {
                    "cookies": cookies,
                    "origins": []
                }

                # Lưu file với tên dựa trên timestamp
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"imported_session_{timestamp}.json"
                session_path = os.path.join(LOCAL_PATH, filename)

                with open(session_path, 'w', encoding='utf-8') as f:
                    json.dump(storage_state, f, indent=2)

                self.log(f"✅ Đã tạo session từ cookie: {filename}")
                self.log(f"📁 Đường dẫn: {session_path}")

                # Hỏi người dùng có muốn đặt tên file theo username không
                reply = QMessageBox.question(dialog, "Đặt tên file",
                    f"Đã tạo session thành công!\n\n"
                    f"Bạn có muốn đặt tên file theo username để sử dụng trực tiếp không?\n"
                    f"(File sẽ được đổi tên thành auth_state_[username].json)",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

                if reply == QMessageBox.StandardButton.Yes:
                    # Hiển thị dialog để nhập username
                    username_dialog = QDialog(dialog)
                    username_dialog.setWindowTitle("Nhập Username")
                    username_dialog.setFixedSize(300, 150)

                    username_layout = QVBoxLayout(username_dialog)
                    username_layout.addWidget(QLabel("Nhập username cho session này:"))

                    username_input = QLineEdit()
                    username_input.setPlaceholderText("Ví dụ: user123")
                    username_layout.addWidget(username_input)

                    username_buttons = QHBoxLayout()
                    ok_btn = QPushButton("OK")
                    cancel_btn = QPushButton("Hủy")

                    def save_with_username():
                        username = username_input.text().strip()
                        if username:
                            # Đổi tên file
                            new_filename = f"auth_state_{username}.json"
                            new_session_path = os.path.join(LOCAL_PATH, new_filename)

                            # Kiểm tra file đã tồn tại chưa
                            if os.path.exists(new_session_path):
                                overwrite = QMessageBox.question(username_dialog, "File đã tồn tại",
                                    f"File {new_filename} đã tồn tại. Bạn có muốn ghi đè không?",
                                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
                                if overwrite != QMessageBox.StandardButton.Yes:
                                    return

                            # Đổi tên file
                            os.rename(session_path, new_session_path)
                            self.log(f"✅ Đã đổi tên session thành: {new_filename}")

                            QMessageBox.information(username_dialog, "Thành công",
                                f"Session đã được đổi tên thành: {new_filename}\n"
                                f"Bạn có thể sử dụng trực tiếp với username '{username}'")

                            username_dialog.accept()
                        else:
                            QMessageBox.warning(username_dialog, "Lỗi", "Vui lòng nhập username!")

                    ok_btn.clicked.connect(save_with_username)
                    cancel_btn.clicked.connect(username_dialog.reject)

                    username_buttons.addWidget(ok_btn)
                    username_buttons.addWidget(cancel_btn)
                    username_layout.addLayout(username_buttons)

                    username_dialog.exec()
                else:
                    QMessageBox.information(dialog, "Thông tin",
                        f"Session đã được lưu với tên: {filename}\n\n"
                        "Bạn có thể đổi tên file theo format: auth_state_[username].json để sử dụng với tài khoản cụ thể")

                # Refresh session list sau khi import thành công
                self.refresh_session_list()
                dialog.accept()

            except Exception as e:
                QMessageBox.critical(dialog, "Lỗi", f"Lỗi khi tạo session: {str(e)}")

        create_button.clicked.connect(create_session)
        cancel_button.clicked.connect(dialog.reject)

        button_layout.addWidget(create_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)

        dialog.exec()

    def parse_cookie_file(self, file_path):
        """Parse cookie từ file JSON hoặc text"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            cookies = []

            # Thử parse JSON trước
            try:
                data = json.loads(content)
                if isinstance(data, list):
                    # Format: [{"name": "...", "value": "...", ...}, ...]
                    for item in data:
                        if isinstance(item, dict) and 'name' in item and 'value' in item:
                            cookie = {
                                "name": item['name'],
                                "value": item['value'],
                                "domain": item.get('domain', '.shopee.vn'),
                                "path": item.get('path', '/'),
                                "httpOnly": item.get('httpOnly', False),
                                "secure": item.get('secure', True)
                            }
                            cookies.append(cookie)
                elif isinstance(data, dict) and 'cookies' in data:
                    # Format: {"cookies": [...]}
                    cookies = data['cookies']

                return cookies

            except json.JSONDecodeError:
                # Thử parse text format
                lines = content.strip().split('\n')

                for line in lines:
                    if '=' in line:
                        parts = line.split('=', 1)
                        if len(parts) == 2:
                            name = parts[0].strip()
                            value = parts[1].strip()
                            cookies.append({
                                "name": name,
                                "value": value,
                                "domain": ".shopee.vn",
                                "path": "/",
                                "httpOnly": False,
                                "secure": True
                            })

                return cookies

        except Exception as e:
            self.log(f"Lỗi khi đọc file cookie: {e}")
            return []







    def extract_browser_cookies_with_log(self, browser_type, log_area):
        """Trích xuất cookie với log chi tiết"""
        try:
            import sqlite3
            import shutil

            # Tìm tất cả các profile có thể có
            if browser_type == "chrome":
                base_path = os.path.expanduser("~\\AppData\\Local\\Google\\Chrome\\User Data")
                cookie_patterns = [
                    os.path.join(base_path, "Default", "Cookies"),
                    os.path.join(base_path, "Profile *", "Cookies")
                ]
            elif browser_type == "edge":
                base_path = os.path.expanduser("~\\AppData\\Local\\Microsoft\\Edge\\User Data")
                cookie_patterns = [
                    os.path.join(base_path, "Default", "Cookies"),
                    os.path.join(base_path, "Profile *", "Cookies")
                ]
            else:
                return []

            # Tìm tất cả file cookie có thể
            cookie_files = []
            for pattern in cookie_patterns:
                if pattern and os.path.exists(pattern):
                    cookie_files.append(pattern)
                    log_area.append(f"   📄 Tìm thấy: {pattern}")

            if not cookie_files:
                log_area.append(f"   ❌ Không tìm thấy file cookie cho {browser_type}")
                return []

            all_cookies = []

            for cookie_path in cookie_files:
                if not os.path.exists(cookie_path):
                    continue

                log_area.append(f"   🔍 Đang quét: {os.path.basename(os.path.dirname(cookie_path))}")

                # Copy database để tránh lock
                temp_path = os.path.join(tempfile.gettempdir(), f"temp_cookies_{browser_type}_{hash(cookie_path)}.db")

                try:
                    shutil.copy2(cookie_path, temp_path)
                    log_area.append(f"   ✅ Copy database thành công")
                except Exception as e:
                    log_area.append(f"   ❌ Không thể copy database: {e}")
                    continue

                cookies = []

                try:
                    conn = sqlite3.connect(temp_path)
                    cursor = conn.cursor()

                    # Query cho Chrome-based browsers (Chrome và Edge)
                    if browser_type in ["chrome", "edge"]:
                        # Kiểm tra cấu trúc bảng trước
                        cursor.execute("PRAGMA table_info(cookies)")
                        columns = [row[1] for row in cursor.fetchall()]
                        log_area.append(f"   📋 Cấu trúc bảng: {len(columns)} cột")

                        if 'host_key' in columns:
                            cursor.execute("""
                                SELECT name, value, host_key, path, is_secure, is_httponly
                                FROM cookies
                                WHERE host_key LIKE '%shopee.vn%' OR host_key LIKE '%creator.shopee.vn%'
                            """)
                        else:
                            # Fallback cho cấu trúc cũ
                            cursor.execute("""
                                SELECT name, value, host, path, secure, httponly
                                FROM cookies
                                WHERE host LIKE '%shopee.vn%' OR host LIKE '%creator.shopee.vn%'
                            """)

                        rows = cursor.fetchall()
                        log_area.append(f"   🔍 Tìm thấy {len(rows)} cookie Shopee")

                        for row in rows:
                            if len(row) >= 6:
                                name, value, domain, path, secure, httponly = row[:6]
                                cookies.append({
                                    "name": name,
                                    "value": value,
                                    "domain": domain if domain.startswith('.') else f".{domain}",
                                    "path": path,
                                    "secure": bool(secure),
                                    "httpOnly": bool(httponly)
                                })

                    conn.close()

                    if cookies:
                        log_area.append(f"   ✅ Trích xuất thành công {len(cookies)} cookie")
                        all_cookies.extend(cookies)

                except Exception as e:
                    log_area.append(f"   ❌ Lỗi khi đọc database: {e}")

                finally:
                    # Xóa file temp
                    if os.path.exists(temp_path):
                        try:
                            os.remove(temp_path)
                        except:
                            pass

            return all_cookies

        except Exception as e:
            log_area.append(f"   ❌ Lỗi tổng quát: {e}")
            return []

    def extract_browser_cookies(self, browser_type):
        """Trích xuất cookie từ một trình duyệt cụ thể"""
        try:
            import sqlite3
            import shutil

            # Tìm tất cả các profile có thể có
            if browser_type == "chrome":
                base_path = os.path.expanduser("~\\AppData\\Local\\Google\\Chrome\\User Data")
                cookie_patterns = [
                    os.path.join(base_path, "Default", "Cookies"),
                    os.path.join(base_path, "Profile *", "Cookies")
                ]
            elif browser_type == "edge":
                base_path = os.path.expanduser("~\\AppData\\Local\\Microsoft\\Edge\\User Data")
                # Tìm tất cả profile và file cookie có thể có
                cookie_patterns = []

                # Thêm các đường dẫn cố định
                cookie_patterns.extend([
                    os.path.join(base_path, "Default", "Cookies"),
                    os.path.join(base_path, "Default", "Network", "Cookies")
                ])

                # Tìm tất cả profile động
                if os.path.exists(base_path):
                    self.log(f"   📂 Quét thư mục: {base_path}")
                    for item in os.listdir(base_path):
                        self.log(f"   📁 Tìm thấy: {item}")
                        if item.startswith("Profile ") or item == "Default":
                            profile_path = os.path.join(base_path, item)
                            if os.path.isdir(profile_path):
                                self.log(f"   ✅ Profile hợp lệ: {item}")
                                # Thêm cả 2 vị trí có thể có cookie
                                cookie_patterns.extend([
                                    os.path.join(profile_path, "Cookies"),
                                    os.path.join(profile_path, "Network", "Cookies")
                                ])
                            else:
                                self.log(f"   ❌ Không phải thư mục: {item}")
                else:
                    self.log(f"   ❌ Thư mục Edge không tồn tại: {base_path}")
            else:
                return []

            # Tìm tất cả file cookie có thể
            cookie_files = []
            self.log(f"   📁 Kiểm tra: {base_path}")

            for pattern in cookie_patterns:
                if pattern and os.path.exists(pattern):
                    self.log(f"   ✅ Tìm thấy: {pattern}")
                    cookie_files.append(pattern)
                elif pattern:
                    self.log(f"   ❌ Không có: {pattern}")

            if not cookie_files:
                self.log(f"   ❌ Không tìm thấy profile nào có file Cookies")
                self.log(f"Không tìm thấy file cookie cho {browser_type}")
                return []

            all_cookies = []

            for cookie_path in cookie_files:
                if not os.path.exists(cookie_path):
                    continue

                self.log(f"Đang quét: {cookie_path}")

                # Copy database để tránh lock
                temp_path = os.path.join(tempfile.gettempdir(), f"temp_cookies_{browser_type}_{hash(cookie_path)}.db")

                try:
                    shutil.copy2(cookie_path, temp_path)
                except Exception as e:
                    self.log(f"Không thể copy database {cookie_path}: {e}")
                    continue

                cookies = []

                try:
                    conn = sqlite3.connect(temp_path)
                    cursor = conn.cursor()

                    # Query cho Chrome-based browsers (Chrome và Edge)
                    if browser_type in ["chrome", "edge"]:
                        # Kiểm tra cấu trúc bảng trước
                        cursor.execute("PRAGMA table_info(cookies)")
                        columns = [row[1] for row in cursor.fetchall()]

                        if 'host_key' in columns:
                            cursor.execute("""
                                SELECT name, value, host_key, path, is_secure, is_httponly
                                FROM cookies
                                WHERE host_key LIKE '%shopee.vn%' OR host_key LIKE '%creator.shopee.vn%'
                            """)
                        else:
                            # Fallback cho cấu trúc cũ
                            cursor.execute("""
                                SELECT name, value, host, path, secure, httponly
                                FROM cookies
                                WHERE host LIKE '%shopee.vn%' OR host LIKE '%creator.shopee.vn%'
                            """)

                        for row in cursor.fetchall():
                            if len(row) >= 6:
                                name, value, domain, path, secure, httponly = row[:6]
                                cookies.append({
                                    "name": name,
                                    "value": value,
                                    "domain": domain if domain.startswith('.') else f".{domain}",
                                    "path": path,
                                    "secure": bool(secure),
                                    "httpOnly": bool(httponly)
                                })

                    conn.close()

                    if cookies:
                        self.log(f"Tìm thấy {len(cookies)} cookie từ {cookie_path}")
                        all_cookies.extend(cookies)

                except Exception as e:
                    self.log(f"Lỗi khi đọc database {cookie_path}: {e}")

                finally:
                    # Xóa file temp
                    if os.path.exists(temp_path):
                        try:
                            os.remove(temp_path)
                        except:
                            pass

            return all_cookies

        except Exception as e:
            self.log(f"Lỗi khi trích xuất cookie từ {browser_type}: {e}")
            return []



    def close_browsers(self, browser_list, log_area):
        """Đóng các trình duyệt để tránh database lock"""
        try:
            import subprocess
            import time

            browser_processes = {
                "chrome": ["chrome.exe", "Google Chrome"],
                "edge": ["msedge.exe", "Microsoft Edge"]
            }

            for browser in browser_list:
                if browser in browser_processes:
                    process_names = browser_processes[browser]

                    for process_name in process_names:
                        try:
                            # Kiểm tra xem process có đang chạy không
                            result = subprocess.run(
                                ["tasklist", "/FI", f"IMAGENAME eq {process_name}"],
                                capture_output=True, text=True, shell=True
                            )

                            if process_name.lower() in result.stdout.lower():
                                log_area.append(f"   🔄 Đang đóng {browser.title()}...")

                                # Thử đóng nhẹ nhàng trước
                                subprocess.run(
                                    ["taskkill", "/IM", process_name, "/T"],
                                    capture_output=True, shell=True
                                )

                                time.sleep(2)  # Đợi process đóng

                                # Kiểm tra lại xem đã đóng chưa
                                result = subprocess.run(
                                    ["tasklist", "/FI", f"IMAGENAME eq {process_name}"],
                                    capture_output=True, text=True, shell=True
                                )

                                if process_name.lower() not in result.stdout.lower():
                                    log_area.append(f"   ✅ Đã đóng {browser.title()}")
                                else:
                                    log_area.append(f"   ⚠️ {browser.title()} vẫn đang chạy")
                            else:
                                log_area.append(f"   ℹ️ {browser.title()} không đang chạy")

                        except Exception as e:
                            log_area.append(f"   ❌ Lỗi khi đóng {browser.title()}: {e}")

            # Đợi thêm một chút để đảm bảo database được unlock
            if browser_list:
                log_area.append("   ⏳ Đợi 3 giây để database được unlock...")
                time.sleep(3)

        except Exception as e:
            log_area.append(f"❌ Lỗi khi đóng trình duyệt: {e}")

    async def validate_session(self, page, session_file):
        """Kiểm tra session có hợp lệ không"""
        try:
            # Đợi trang load
            await page.wait_for_load_state('networkidle', timeout=10000)

            # Kiểm tra URL hiện tại
            current_url = page.url
            self.log(f"🔍 Kiểm tra session - URL hiện tại: {current_url}")

            # Nếu bị redirect về trang login, session không hợp lệ
            if 'login' in current_url.lower() or 'signin' in current_url.lower():
                self.log("❌ Session không hợp lệ - bị redirect về trang login")
                return False

            # Kiểm tra xem có element đăng nhập không
            login_elements = [
                'input[type="email"]',
                'input[type="password"]',
                'input[placeholder*="email"]',
                'input[placeholder*="password"]',
                '.login-form',
                '.signin-form'
            ]

            for selector in login_elements:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        self.log("❌ Session không hợp lệ - tìm thấy form đăng nhập")
                        return False
                except:
                    continue

            # Kiểm tra cookie quan trọng
            cookies = await page.context.cookies()
            important_cookies = ['SPC_F', 'SPC_T_ID', 'shopee_token']
            cookie_names = [c['name'] for c in cookies]

            has_important_cookies = any(name in cookie_names for name in important_cookies)

            if not has_important_cookies:
                self.log("❌ Session không hợp lệ - thiếu cookie quan trọng")
                return False

            # Nếu tất cả kiểm tra đều pass
            if 'creator.shopee.vn' in current_url:
                self.log("✅ Session hợp lệ")
                # Làm mới cookie trong session file
                await self.refresh_session_cookies(page, session_file)
                return True
            else:
                self.log("⚠️ Session có thể không hợp lệ - không ở trang creator")
                return False

        except Exception as e:
            self.log(f"❌ Lỗi khi kiểm tra session: {e}")
            return False

    async def refresh_session_cookies(self, page, session_file):
        """Làm mới cookie trong session file"""
        try:
            # Lấy cookie hiện tại từ browser
            current_cookies = await page.context.cookies()

            # Đọc session file hiện tại
            with open(session_file, 'r', encoding='utf-8') as f:
                session_data = json.load(f)

            # Cập nhật cookie
            session_data['cookies'] = current_cookies

            # Lưu lại session file
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2)

            self.log("✅ Đã làm mới cookie trong session file")
            return True

        except Exception as e:
            self.log(f"❌ Lỗi khi làm mới session: {e}")
            return False









    def manage_session_files(self):
        """Quản lý và gán session files cho tài khoản"""
        dialog = QDialog(self)
        dialog.setWindowTitle("Quản lý Session Files")
        dialog.setFixedSize(800, 600)

        layout = QVBoxLayout(dialog)

        # Hướng dẫn
        instruction_label = QLabel("""
<b>Quản lý Session Files</b><br><br>
Đây là danh sách tất cả session files có sẵn. Bạn có thể:<br>
• Xem thông tin chi tiết của từng session<br>
• Gán session cho tài khoản cụ thể<br>
• Đổi tên session để sử dụng tự động<br>
• Xóa session không cần thiết
        """)
        instruction_label.setWordWrap(True)
        layout.addWidget(instruction_label)

        # Tìm tất cả session files
        session_files = []
        if os.path.exists(LOCAL_PATH):
            for file in os.listdir(LOCAL_PATH):
                if file.endswith('.json') and ('session' in file.lower() or 'auth_state' in file.lower()):
                    file_path = os.path.join(LOCAL_PATH, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        if 'cookies' in data:
                            session_files.append({
                                'filename': file,
                                'path': file_path,
                                'data': data,
                                'size': os.path.getsize(file_path),
                                'modified': datetime.fromtimestamp(os.path.getmtime(file_path))
                            })
                    except:
                        continue

        if not session_files:
            QMessageBox.information(dialog, "Thông tin", "Không tìm thấy session file nào!")
            return

        # Tạo table để hiển thị session files
        table = QTableWidget()
        table.setRowCount(len(session_files))
        table.setColumnCount(5)
        table.setHorizontalHeaderLabels(['Tên File', 'Số Cookie', 'Kích thước', 'Ngày tạo', 'Trạng thái'])

        # Điền dữ liệu vào table
        for i, session in enumerate(session_files):
            # Tên file
            table.setItem(i, 0, QTableWidgetItem(session['filename']))

            # Số cookie
            cookie_count = len(session['data'].get('cookies', []))
            table.setItem(i, 1, QTableWidgetItem(str(cookie_count)))

            # Kích thước
            size_kb = session['size'] / 1024
            table.setItem(i, 2, QTableWidgetItem(f"{size_kb:.1f} KB"))

            # Ngày tạo
            table.setItem(i, 3, QTableWidgetItem(session['modified'].strftime("%Y-%m-%d %H:%M")))

            # Trạng thái
            if session['filename'].startswith('auth_state_'):
                username = session['filename'].replace('auth_state_', '').replace('.json', '')
                status = f"✅ Gán cho: {username}"
            else:
                status = "⚠️ Chưa gán"
            table.setItem(i, 4, QTableWidgetItem(status))

        # Tự động điều chỉnh kích thước cột
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)

        layout.addWidget(table)

        # Buttons
        button_layout = QHBoxLayout()

        assign_button = QPushButton("Gán cho tài khoản")
        view_button = QPushButton("Xem chi tiết")
        rename_button = QPushButton("Đổi tên")
        delete_button = QPushButton("Xóa")
        close_button = QPushButton("Đóng")

        def assign_to_account():
            current_row = table.currentRow()
            if current_row < 0:
                QMessageBox.warning(dialog, "Lỗi", "Vui lòng chọn một session file!")
                return

            session = session_files[current_row]

            # Hiển thị dialog chọn tài khoản
            account_dialog = QDialog(dialog)
            account_dialog.setWindowTitle("Chọn tài khoản")
            account_dialog.setFixedSize(400, 300)

            account_layout = QVBoxLayout(account_dialog)
            account_layout.addWidget(QLabel(f"Gán session '{session['filename']}' cho tài khoản:"))

            account_combo = QComboBox()
            for account in self.accounts:
                account_combo.addItem(account['username'])

            account_layout.addWidget(account_combo)

            account_buttons = QHBoxLayout()
            ok_btn = QPushButton("Gán")
            cancel_btn = QPushButton("Hủy")

            def do_assign():
                selected_username = account_combo.currentText()
                new_filename = f"auth_state_{selected_username}.json"
                new_path = os.path.join(LOCAL_PATH, new_filename)

                # Kiểm tra file đã tồn tại
                if os.path.exists(new_path):
                    reply = QMessageBox.question(account_dialog, "File đã tồn tại",
                        f"Tài khoản '{selected_username}' đã có session. Bạn có muốn ghi đè không?",
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
                    if reply != QMessageBox.StandardButton.Yes:
                        return

                # Đổi tên file
                try:
                    if os.path.exists(new_path):
                        os.remove(new_path)
                    os.rename(session['path'], new_path)

                    self.log(f"✅ Đã gán session cho tài khoản '{selected_username}'")
                    QMessageBox.information(account_dialog, "Thành công",
                        f"Đã gán session cho tài khoản '{selected_username}' thành công!")

                    # Refresh session list sau khi gán
                    self.refresh_session_list()
                    account_dialog.accept()
                    dialog.accept()  # Đóng dialog chính để refresh

                except Exception as e:
                    QMessageBox.critical(account_dialog, "Lỗi", f"Lỗi khi gán session: {e}")

            ok_btn.clicked.connect(do_assign)
            cancel_btn.clicked.connect(account_dialog.reject)

            account_buttons.addWidget(ok_btn)
            account_buttons.addWidget(cancel_btn)
            account_layout.addLayout(account_buttons)

            account_dialog.exec()

        def view_details():
            current_row = table.currentRow()
            if current_row < 0:
                QMessageBox.warning(dialog, "Lỗi", "Vui lòng chọn một session file!")
                return

            session = session_files[current_row]
            cookies = session['data'].get('cookies', [])

            # Tạo dialog hiển thị chi tiết
            detail_dialog = QDialog(dialog)
            detail_dialog.setWindowTitle(f"Chi tiết: {session['filename']}")
            detail_dialog.setFixedSize(600, 500)

            detail_layout = QVBoxLayout(detail_dialog)

            # Thông tin tổng quan
            info_text = f"""
<b>Tên file:</b> {session['filename']}<br>
<b>Kích thước:</b> {session['size']/1024:.1f} KB<br>
<b>Ngày tạo:</b> {session['modified'].strftime("%Y-%m-%d %H:%M:%S")}<br>
<b>Số cookie:</b> {len(cookies)}<br><br>
<b>Danh sách cookie:</b>
            """

            info_label = QLabel(info_text)
            info_label.setWordWrap(True)
            detail_layout.addWidget(info_label)

            # Danh sách cookie
            cookie_list = QTextEdit()
            cookie_list.setReadOnly(True)
            cookie_list.setMaximumHeight(300)

            cookie_text = ""
            important_cookies = ['SPC_F', 'SPC_T_ID', 'SPC_R_T_ID', 'shopee_token', 'userid', 'username']

            for cookie in cookies:
                name = cookie.get('name', '')
                domain = cookie.get('domain', '')
                is_important = name in important_cookies

                if is_important:
                    cookie_text += f"🔑 {name} ({domain})\n"
                else:
                    cookie_text += f"   {name} ({domain})\n"

            cookie_list.setText(cookie_text)
            detail_layout.addWidget(cookie_list)

            # Nút đóng
            close_btn = QPushButton("Đóng")
            close_btn.clicked.connect(detail_dialog.reject)
            detail_layout.addWidget(close_btn)

            detail_dialog.exec()

        def rename_session():
            current_row = table.currentRow()
            if current_row < 0:
                QMessageBox.warning(dialog, "Lỗi", "Vui lòng chọn một session file!")
                return

            session = session_files[current_row]

            # Dialog nhập tên mới
            new_name, ok = QInputDialog.getText(dialog, "Đổi tên session",
                f"Nhập tên mới cho session:\n(Hiện tại: {session['filename']})",
                text=session['filename'].replace('.json', ''))

            if ok and new_name.strip():
                new_filename = f"{new_name.strip()}.json"
                new_path = os.path.join(LOCAL_PATH, new_filename)

                if os.path.exists(new_path):
                    QMessageBox.warning(dialog, "Lỗi", f"File '{new_filename}' đã tồn tại!")
                    return

                try:
                    os.rename(session['path'], new_path)
                    self.log(f"✅ Đã đổi tên session: {session['filename']} → {new_filename}")
                    QMessageBox.information(dialog, "Thành công", f"Đã đổi tên thành: {new_filename}")
                    # Refresh session list sau khi đổi tên
                    self.refresh_session_list()
                    dialog.accept()  # Đóng để refresh

                except Exception as e:
                    QMessageBox.critical(dialog, "Lỗi", f"Lỗi khi đổi tên: {e}")

        def delete_session():
            current_row = table.currentRow()
            if current_row < 0:
                QMessageBox.warning(dialog, "Lỗi", "Vui lòng chọn một session file!")
                return

            session = session_files[current_row]

            reply = QMessageBox.question(dialog, "Xác nhận xóa",
                f"Bạn có chắc muốn xóa session '{session['filename']}'?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

            if reply == QMessageBox.StandardButton.Yes:
                try:
                    os.remove(session['path'])
                    self.log(f"✅ Đã xóa session: {session['filename']}")
                    QMessageBox.information(dialog, "Thành công", "Đã xóa session!")
                    # Refresh session list sau khi xóa
                    self.refresh_session_list()
                    dialog.accept()  # Đóng để refresh

                except Exception as e:
                    QMessageBox.critical(dialog, "Lỗi", f"Lỗi khi xóa: {e}")

        assign_button.clicked.connect(assign_to_account)
        view_button.clicked.connect(view_details)
        rename_button.clicked.connect(rename_session)
        delete_button.clicked.connect(delete_session)
        close_button.clicked.connect(dialog.reject)

        button_layout.addWidget(assign_button)
        button_layout.addWidget(view_button)
        button_layout.addWidget(rename_button)
        button_layout.addWidget(delete_button)
        button_layout.addWidget(close_button)
        layout.addLayout(button_layout)

        dialog.exec()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ShopeeScraperApp()
    window.show()
    sys.exit(app.exec())