#!/usr/bin/env python3
"""
Test script để kiểm tra Batch Operations
"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_batch_operations():
    """Test basic batch operations"""
    try:
        print("🚀 Testing Batch Operations...")
        
        # Import the main class
        from top150 import ShopeeScraperApp
        
        print("✅ Import thành công")
        
        # Create app instance
        app = ShopeeScraperApp()
        print("✅ Tạo app instance thành công")
        
        # Test batch engine
        if hasattr(app, 'batch_engine'):
            print("✅ Batch engine tồn tại")
            
            # Test add request
            app.batch_engine.add_request({'test': 'request'})
            print(f"✅ Pending requests: {len(app.batch_engine.pending_requests)}")
            
            # Test clear
            app.batch_engine.clear()
            print(f"✅ After clear: {len(app.batch_engine.pending_requests)}")
        else:
            print("❌ Batch engine không tồn tại")
            
        print("✅ Test hoàn thành")
        
    except Exception as e:
        print(f"❌ Lỗi trong test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_batch_operations())
